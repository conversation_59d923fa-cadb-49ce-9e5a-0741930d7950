[{"/root/nuclei/nuclei-portal/src/app/api/auth/login/route.ts": "1", "/root/nuclei/nuclei-portal/src/app/api/auth/register/route.ts": "2", "/root/nuclei/nuclei-portal/src/app/api/scans/[id]/route.ts": "3", "/root/nuclei/nuclei-portal/src/app/api/scans/route.ts": "4", "/root/nuclei/nuclei-portal/src/app/layout.tsx": "5", "/root/nuclei/nuclei-portal/src/app/page.tsx": "6", "/root/nuclei/nuclei-portal/src/lib/auth.ts": "7", "/root/nuclei/nuclei-portal/src/lib/database.ts": "8", "/root/nuclei/nuclei-portal/src/lib/nuclei.ts": "9", "/root/nuclei/nuclei-portal/src/lib/utils.ts": "10"}, {"size": 826, "mtime": 1750394194975, "results": "11", "hashOfConfig": "12"}, {"size": 1028, "mtime": 1750394203373, "results": "13", "hashOfConfig": "12"}, {"size": 2824, "mtime": 1750394233489, "results": "14", "hashOfConfig": "12"}, {"size": 2902, "mtime": 1750394217607, "results": "15", "hashOfConfig": "12"}, {"size": 689, "mtime": 1750393910303, "results": "16", "hashOfConfig": "12"}, {"size": 7329, "mtime": 1750394987704, "results": "17", "hashOfConfig": "12"}, {"size": 2698, "mtime": 1750395010102, "results": "18", "hashOfConfig": "12"}, {"size": 4555, "mtime": 1750394124580, "results": "19", "hashOfConfig": "12"}, {"size": 6540, "mtime": 1750395062237, "results": "20", "hashOfConfig": "12"}, {"size": 166, "mtime": 1750394029977, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lzc2im", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/root/nuclei/nuclei-portal/src/app/api/auth/login/route.ts", [], [], "/root/nuclei/nuclei-portal/src/app/api/auth/register/route.ts", [], [], "/root/nuclei/nuclei-portal/src/app/api/scans/[id]/route.ts", [], [], "/root/nuclei/nuclei-portal/src/app/api/scans/route.ts", [], [], "/root/nuclei/nuclei-portal/src/app/layout.tsx", [], [], "/root/nuclei/nuclei-portal/src/app/page.tsx", ["52", "53", "54", "55"], [], "/root/nuclei/nuclei-portal/src/lib/auth.ts", ["56"], [], "/root/nuclei/nuclei-portal/src/lib/database.ts", [], [], "/root/nuclei/nuclei-portal/src/lib/nuclei.ts", ["57", "58"], ["59"], "/root/nuclei/nuclei-portal/src/lib/utils.ts", [], [], {"ruleId": "60", "severity": 2, "message": "61", "line": 8, "column": 36, "nodeType": "62", "messageId": "63", "endLine": 8, "endColumn": 39, "suggestions": "64"}, {"ruleId": "60", "severity": 2, "message": "61", "line": 25, "column": 94, "nodeType": "62", "messageId": "63", "endLine": 25, "endColumn": 97, "suggestions": "65"}, {"ruleId": "60", "severity": 2, "message": "61", "line": 131, "column": 33, "nodeType": "62", "messageId": "63", "endLine": 131, "endColumn": 36, "suggestions": "66"}, {"ruleId": "60", "severity": 2, "message": "61", "line": 202, "column": 29, "nodeType": "62", "messageId": "63", "endLine": 202, "endColumn": 32, "suggestions": "67"}, {"ruleId": "68", "severity": 2, "message": "69", "line": 93, "column": 14, "nodeType": null, "messageId": "70", "endLine": 93, "endColumn": 19}, {"ruleId": "60", "severity": 2, "message": "61", "line": 17, "column": 30, "nodeType": "62", "messageId": "63", "endLine": 17, "endColumn": 33, "suggestions": "71"}, {"ruleId": "60", "severity": 2, "message": "61", "line": 223, "column": 30, "nodeType": "62", "messageId": "63", "endLine": 223, "endColumn": 33, "suggestions": "72"}, {"ruleId": "73", "severity": 2, "message": "74", "line": 232, "column": 28, "nodeType": "75", "messageId": "76", "endLine": 232, "endColumn": 52, "suppressions": "77"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["78", "79"], ["80", "81"], ["82", "83"], ["84", "85"], "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", ["86", "87"], ["88", "89"], "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["90"], {"messageId": "91", "fix": "92", "desc": "93"}, {"messageId": "94", "fix": "95", "desc": "96"}, {"messageId": "91", "fix": "97", "desc": "93"}, {"messageId": "94", "fix": "98", "desc": "96"}, {"messageId": "91", "fix": "99", "desc": "93"}, {"messageId": "94", "fix": "100", "desc": "96"}, {"messageId": "91", "fix": "101", "desc": "93"}, {"messageId": "94", "fix": "102", "desc": "96"}, {"messageId": "91", "fix": "103", "desc": "93"}, {"messageId": "94", "fix": "104", "desc": "96"}, {"messageId": "91", "fix": "105", "desc": "93"}, {"messageId": "94", "fix": "106", "desc": "96"}, {"kind": "107", "justification": "108"}, "suggestUnknown", {"range": "109", "text": "110"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "111", "text": "112"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "113", "text": "110"}, {"range": "114", "text": "112"}, {"range": "115", "text": "110"}, {"range": "116", "text": "112"}, {"range": "117", "text": "110"}, {"range": "118", "text": "112"}, {"range": "119", "text": "110"}, {"range": "120", "text": "112"}, {"range": "121", "text": "110"}, {"range": "122", "text": "112"}, "directive", "", [272, 275], "unknown", [272, 275], "never", [696, 699], [696, 699], [4328, 4331], [4328, 4331], [6814, 6817], [6814, 6817], [420, 423], [420, 423], [6081, 6084], [6081, 6084]]