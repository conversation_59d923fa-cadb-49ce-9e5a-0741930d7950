[{"/root/nuclei/nuclei-portal/src/app/api/auth/login/route.ts": "1", "/root/nuclei/nuclei-portal/src/app/api/auth/register/route.ts": "2", "/root/nuclei/nuclei-portal/src/app/api/scans/[id]/route.ts": "3", "/root/nuclei/nuclei-portal/src/app/api/scans/route.ts": "4", "/root/nuclei/nuclei-portal/src/app/layout.tsx": "5", "/root/nuclei/nuclei-portal/src/app/page.tsx": "6", "/root/nuclei/nuclei-portal/src/lib/auth.ts": "7", "/root/nuclei/nuclei-portal/src/lib/database.ts": "8", "/root/nuclei/nuclei-portal/src/lib/nuclei.ts": "9", "/root/nuclei/nuclei-portal/src/lib/utils.ts": "10", "/root/nuclei/nuclei-portal/src/app/api/socket/route.ts": "11", "/root/nuclei/nuclei-portal/src/app/scan/[id]/page.tsx": "12", "/root/nuclei/nuclei-portal/src/components/ScanProgress.tsx": "13", "/root/nuclei/nuclei-portal/src/hooks/useWebSocket.ts": "14", "/root/nuclei/nuclei-portal/src/lib/websocket.ts": "15"}, {"size": 826, "mtime": 1750394194975, "results": "16", "hashOfConfig": "17"}, {"size": 1028, "mtime": 1750394203373, "results": "18", "hashOfConfig": "17"}, {"size": 2940, "mtime": 1750395331055, "results": "19", "hashOfConfig": "17"}, {"size": 2902, "mtime": 1750394217607, "results": "20", "hashOfConfig": "17"}, {"size": 689, "mtime": 1750393910303, "results": "21", "hashOfConfig": "17"}, {"size": 19306, "mtime": 1750408539352, "results": "22", "hashOfConfig": "17"}, {"size": 2705, "mtime": 1750395385992, "results": "23", "hashOfConfig": "17"}, {"size": 2987, "mtime": 1750399627726, "results": "24", "hashOfConfig": "17"}, {"size": 11332, "mtime": 1750408813378, "results": "25", "hashOfConfig": "17"}, {"size": 166, "mtime": 1750394029977, "results": "26", "hashOfConfig": "17"}, {"size": 275, "mtime": 1750408574279, "results": "27", "hashOfConfig": "17"}, {"size": 6087, "mtime": 1750408380132, "results": "28", "hashOfConfig": "17"}, {"size": 7137, "mtime": 1750408351597, "results": "29", "hashOfConfig": "17"}, {"size": 2002, "mtime": 1750408320137, "results": "30", "hashOfConfig": "17"}, {"size": 3590, "mtime": 1750408157616, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "p8iuni", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/root/nuclei/nuclei-portal/src/app/api/auth/login/route.ts", [], [], "/root/nuclei/nuclei-portal/src/app/api/auth/register/route.ts", [], [], "/root/nuclei/nuclei-portal/src/app/api/scans/[id]/route.ts", [], [], "/root/nuclei/nuclei-portal/src/app/api/scans/route.ts", [], [], "/root/nuclei/nuclei-portal/src/app/layout.tsx", [], [], "/root/nuclei/nuclei-portal/src/app/page.tsx", [], [], "/root/nuclei/nuclei-portal/src/lib/auth.ts", [], [], "/root/nuclei/nuclei-portal/src/lib/database.ts", [], [], "/root/nuclei/nuclei-portal/src/lib/nuclei.ts", [], ["77"], "/root/nuclei/nuclei-portal/src/lib/utils.ts", [], [], "/root/nuclei/nuclei-portal/src/app/api/socket/route.ts", [], [], "/root/nuclei/nuclei-portal/src/app/scan/[id]/page.tsx", ["78"], [], "/root/nuclei/nuclei-portal/src/components/ScanProgress.tsx", ["79"], [], "/root/nuclei/nuclei-portal/src/hooks/useWebSocket.ts", [], [], "/root/nuclei/nuclei-portal/src/lib/websocket.ts", [], [], {"ruleId": "80", "severity": 2, "message": "81", "line": 363, "column": 28, "nodeType": "82", "messageId": "83", "endLine": 363, "endColumn": 52, "suppressions": "84"}, {"ruleId": "85", "severity": 1, "message": "86", "line": 28, "column": 6, "nodeType": "87", "endLine": 28, "endColumn": 14, "suggestions": "88"}, {"ruleId": "85", "severity": 1, "message": "89", "line": 31, "column": 6, "nodeType": "87", "endLine": 31, "endColumn": 45, "suggestions": "90"}, "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["91"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchScanDetails'. Either include it or remove the dependency array.", "ArrayExpression", ["92"], "React Hook useEffect has a missing dependency: 'progress.error'. Either include it or remove the dependency array.", ["93"], {"kind": "94", "justification": "95"}, {"desc": "96", "fix": "97"}, {"desc": "98", "fix": "99"}, "directive", "", "Update the dependencies array to be: [fetchScanDetails, scanId]", {"range": "100", "text": "101"}, "Update the dependencies array to be: [progress?.status, onComplete, onError, progress.error]", {"range": "102", "text": "103"}, [771, 779], "[fetchScanDetails, scanId]", [811, 850], "[progress?.status, onComplete, onError, progress.error]"]