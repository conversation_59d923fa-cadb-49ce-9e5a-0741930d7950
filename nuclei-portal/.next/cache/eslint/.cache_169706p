[{"/root/nuclei/nuclei-portal/src/app/api/auth/login/route.ts": "1", "/root/nuclei/nuclei-portal/src/app/api/auth/register/route.ts": "2", "/root/nuclei/nuclei-portal/src/app/api/scans/[id]/route.ts": "3", "/root/nuclei/nuclei-portal/src/app/api/scans/route.ts": "4", "/root/nuclei/nuclei-portal/src/app/layout.tsx": "5", "/root/nuclei/nuclei-portal/src/app/page.tsx": "6", "/root/nuclei/nuclei-portal/src/lib/auth.ts": "7", "/root/nuclei/nuclei-portal/src/lib/database.ts": "8", "/root/nuclei/nuclei-portal/src/lib/nuclei.ts": "9", "/root/nuclei/nuclei-portal/src/lib/utils.ts": "10"}, {"size": 826, "mtime": 1750394194975, "results": "11", "hashOfConfig": "12"}, {"size": 1028, "mtime": 1750394203373, "results": "13", "hashOfConfig": "12"}, {"size": 2940, "mtime": 1750395331055, "results": "14", "hashOfConfig": "12"}, {"size": 2902, "mtime": 1750394217607, "results": "15", "hashOfConfig": "12"}, {"size": 689, "mtime": 1750393910303, "results": "16", "hashOfConfig": "12"}, {"size": 7329, "mtime": 1750394987704, "results": "17", "hashOfConfig": "12"}, {"size": 2705, "mtime": 1750395385992, "results": "18", "hashOfConfig": "12"}, {"size": 5192, "mtime": 1750395493181, "results": "19", "hashOfConfig": "12"}, {"size": 6540, "mtime": 1750395062237, "results": "20", "hashOfConfig": "12"}, {"size": 166, "mtime": 1750394029977, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "p8iuni", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/root/nuclei/nuclei-portal/src/app/api/auth/login/route.ts", [], [], "/root/nuclei/nuclei-portal/src/app/api/auth/register/route.ts", [], [], "/root/nuclei/nuclei-portal/src/app/api/scans/[id]/route.ts", [], [], "/root/nuclei/nuclei-portal/src/app/api/scans/route.ts", [], [], "/root/nuclei/nuclei-portal/src/app/layout.tsx", [], [], "/root/nuclei/nuclei-portal/src/app/page.tsx", [], [], "/root/nuclei/nuclei-portal/src/lib/auth.ts", [], [], "/root/nuclei/nuclei-portal/src/lib/database.ts", [], [], "/root/nuclei/nuclei-portal/src/lib/nuclei.ts", [], ["52"], "/root/nuclei/nuclei-portal/src/lib/utils.ts", [], [], {"ruleId": "53", "severity": 2, "message": "54", "line": 232, "column": 28, "nodeType": "55", "messageId": "56", "endLine": 232, "endColumn": 52, "suppressions": "57"}, "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["58"], {"kind": "59", "justification": "60"}, "directive", ""]