{"ABeeZee": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "ADLaM Display": {"weights": ["400"], "styles": ["normal"], "subsets": ["adlam", "latin", "latin-ext"]}, "AR One Sans": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "ARRR", "min": 10, "max": 60, "defaultValue": 10}, {"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Abel": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Abhaya Libre": {"weights": ["400", "500", "600", "700", "800"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "sinhala"]}, "Aboreto": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Abril Fatface": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Abyssinica SIL": {"weights": ["400"], "styles": ["normal"], "subsets": ["ethiopic", "latin", "latin-ext"]}, "Aclonica": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Acme": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Actor": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Adamina": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Advent Pro": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 100, "max": 200, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext"]}, "Afacad": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic-ext", "latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Afacad Flux": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal"], "axes": [{"tag": "slnt", "min": -14, "max": 14, "defaultValue": 0}, {"tag": "wght", "min": 100, "max": 1000, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Agbalumo": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Agdasima": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Agu Display": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "MORF", "min": 0, "max": 60, "defaultValue": 0}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Aguafina Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Akatab": {"weights": ["400", "500", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "tif<PERSON><PERSON>"]}, "Akaya Kanadaka": {"weights": ["400"], "styles": ["normal"], "subsets": ["kannada", "latin", "latin-ext"]}, "Akaya Telivigala": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "telugu"]}, "Akronim": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Akshar": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Aladin": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Alata": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Alatsi": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Albert Sans": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Aldrich": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Alef": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["hebrew", "latin", "latin-ext"]}, "Alegreya": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Alegreya SC": {"weights": ["400", "500", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Alegreya Sans": {"weights": ["100", "300", "400", "500", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Alegreya Sans SC": {"weights": ["100", "300", "400", "500", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Aleo": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Alex Brush": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Alexandria": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext", "vietnamese"]}, "Alfa Slab One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Alice": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "Alike": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Alike Angular": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Alkalami": {"weights": ["400"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext"]}, "Alkatra": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["bengali", "<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext", "oriya"]}, "Allan": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Allerta": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Allerta Stencil": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Allison": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Allura": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Almarai": {"weights": ["300", "400", "700", "800"], "styles": ["normal"], "subsets": ["arabic", "latin"]}, "Almendra": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Almendra Display": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Almendra SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Alumni Sans": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Alumni Sans Collegiate One": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Alumni Sans Inline One": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Alumni Sans Pinstripe": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Amarante": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Amaranth": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "Amatic SC": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["cyrillic", "hebrew", "latin", "latin-ext", "vietnamese"]}, "Amethysta": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Amiko": {"weights": ["400", "600", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Amiri": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["arabic", "latin", "latin-ext"]}, "Amiri Quran": {"weights": ["400"], "styles": ["normal"], "subsets": ["arabic", "latin"]}, "Amita": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Anaheim": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Andada Pro": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 840, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Andika": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Anek Bangla": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["bengali", "latin", "latin-ext"]}, "Anek Devanagari": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Anek Gujarati": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["gujarati", "latin", "latin-ext"]}, "Anek Gurmukhi": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["gur<PERSON><PERSON>i", "latin", "latin-ext"]}, "Anek Kannada": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["kannada", "latin", "latin-ext"]}, "Anek Latin": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Anek Malayalam": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "malayalam"]}, "Anek Odia": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "oriya"]}, "Anek Tamil": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "tamil"]}, "Anek Telugu": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "telugu"]}, "Angkor": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Annapurna SIL": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext", "math", "symbols"]}, "Annie Use Your Telescope": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Anonymous Pro": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "greek", "latin", "latin-ext"]}, "Anta": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Antic": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Antic Didone": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Antic Slab": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Anton": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Anton SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Antonio": {"weights": ["100", "200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Anuphan": {"weights": ["100", "200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Anybody": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 50, "max": 150, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Aoboshi One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Arapey": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "Arbutus": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Arbutus Slab": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Architects Daughter": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Archivo": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 62, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Archivo Black": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Archivo Narrow": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Are You Serious": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Aref Ruqaa": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext"]}, "Aref Ruqaa Ink": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext"]}, "Arima": {"weights": ["100", "200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 700, "defaultValue": 400}], "subsets": ["greek", "greek-ext", "latin", "latin-ext", "malayalam", "tamil", "vietnamese"]}, "Arimo": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "hebrew", "latin", "latin-ext", "vietnamese"]}, "Arizonia": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Armata": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Arsenal": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Arsenal SC": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Artifika": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Arvo": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "Arya": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Asap": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Asap Condensed": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Asar": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Asset": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic-ext", "latin", "latin-ext", "math", "symbols"]}, "Assistant": {"weights": ["200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 800, "defaultValue": 400}], "subsets": ["hebrew", "latin", "latin-ext"]}, "Astloch": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Asul": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Athiti": {"weights": ["200", "300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Atkinson Hyperlegible": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Atma": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["bengali", "latin", "latin-ext"]}, "Atomic Age": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Aubrey": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Audiowide": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Autour One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Average": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Average Sans": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Averia Gruesa Libre": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Averia Libre": {"weights": ["300", "400", "700"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "Averia Sans Libre": {"weights": ["300", "400", "700"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "Averia Serif Libre": {"weights": ["300", "400", "700"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "Azeret Mono": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "B612": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "B612 Mono": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "BIZ UDGothic": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["cyrillic", "greek-ext", "latin", "latin-ext"]}, "BIZ UDMincho": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["cyrillic", "greek-ext", "latin", "latin-ext"]}, "BIZ UDPGothic": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["cyrillic", "greek-ext", "latin", "latin-ext"]}, "BIZ UDPMincho": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["cyrillic", "greek-ext", "latin", "latin-ext"]}, "Babylonica": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Bacasime Antique": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Bad Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Badeen Display": {"weights": ["400"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext"]}, "Bagel Fat One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Bahiana": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Bahianita": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Bai Jamjuree": {"weights": ["200", "300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Bakbak One": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Ballet": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "opsz", "min": 16, "max": 72, "defaultValue": 16}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Baloo 2": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext", "vietnamese"]}, "Baloo Bhai 2": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["gujarati", "latin", "latin-ext", "vietnamese"]}, "Baloo Bhaijaan 2": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext", "vietnamese"]}, "Baloo Bhaina 2": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "oriya", "vietnamese"]}, "Baloo Chettan 2": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "malayalam", "vietnamese"]}, "Baloo Da 2": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["bengali", "latin", "latin-ext", "vietnamese"]}, "Baloo Paaji 2": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["gur<PERSON><PERSON>i", "latin", "latin-ext", "vietnamese"]}, "Baloo Tamma 2": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["kannada", "latin", "latin-ext", "vietnamese"]}, "Baloo Tammudu 2": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "telugu", "vietnamese"]}, "Baloo Thambi 2": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "tamil", "vietnamese"]}, "Balsamiq Sans": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "Balthazar": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Bangers": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Barlow": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Barlow Condensed": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Barlow Semi Condensed": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Barriecito": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Barrio": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Basic": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Baskervville": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Baskervville SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Battambang": {"weights": ["100", "300", "400", "700", "900"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Baumans": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Bayon": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Be Vietnam Pro": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Beau Rivage": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Bebas Neue": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Beiruti": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext", "vietnamese"]}, "Belanosima": {"weights": ["400", "600", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Belgrano": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Bellefair": {"weights": ["400"], "styles": ["normal"], "subsets": ["hebrew", "latin", "latin-ext"]}, "Belleza": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Bellota": {"weights": ["300", "400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Bellota Text": {"weights": ["300", "400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "BenchNine": {"weights": ["300", "400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Benne": {"weights": ["400"], "styles": ["normal"], "subsets": ["kannada", "latin", "latin-ext"]}, "Bentham": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Berkshire Swash": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Besley": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Beth Ellen": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Bevan": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "BhuTuka Expanded One": {"weights": ["400"], "styles": ["normal"], "subsets": ["gur<PERSON><PERSON>i", "latin", "latin-ext"]}, "Big Shoulders Display": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Big Shoulders Inline Display": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Big Shoulders Inline Text": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Big Shoulders Stencil Display": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Big Shoulders Stencil Text": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Big Shoulders Text": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Bigelow Rules": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Bigshot One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Bilbo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Bilbo Swash Caps": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "BioRhyme": {"weights": ["200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 100, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 200, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "BioRhyme Expanded": {"weights": ["200", "300", "400", "700", "800"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Birthstone": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Birthstone Bounce": {"weights": ["400", "500"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Biryani": {"weights": ["200", "300", "400", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Bitter": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Black And White Picture": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Black Han Sans": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Black Ops One": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Blaka": {"weights": ["400"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext"]}, "Blaka Hollow": {"weights": ["400"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext"]}, "Blaka Ink": {"weights": ["400"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext"]}, "Blinker": {"weights": ["100", "200", "300", "400", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Bodoni Moda": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "opsz", "min": 6, "max": 96, "defaultValue": 11}, {"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Bodoni Moda SC": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "opsz", "min": 6, "max": 96, "defaultValue": 11}, {"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Bokor": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Bona Nova": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "hebrew", "latin", "latin-ext", "vietnamese"]}, "Bona Nova SC": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "hebrew", "latin", "latin-ext", "vietnamese"]}, "Bonbon": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Bonheur Royale": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Boogaloo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Borel": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Bowlby One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Bowlby One SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Braah One": {"weights": ["400"], "styles": ["normal"], "subsets": ["gur<PERSON><PERSON>i", "latin", "latin-ext", "vietnamese"]}, "Brawler": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Bree Serif": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Bricolage Grotesque": {"weights": ["200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "opsz", "min": 12, "max": 96, "defaultValue": 14}, {"tag": "wdth", "min": 75, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 200, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Bruno Ace": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Bruno Ace SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Brygada 1918": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "vietnamese"]}, "Bubblegum Sans": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Bubbler One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Buda": {"weights": ["300"], "styles": ["normal"], "subsets": ["latin"]}, "Buenard": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Bungee": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Bungee Hairline": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Bungee Inline": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Bungee Outline": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Bungee Shade": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Bungee Spice": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Bungee Tint": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Butcherman": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Butterfly Kids": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Cabin": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 75, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Cabin Condensed": {"weights": ["400", "500", "600", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Cabin Sketch": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Cactus Classical Serif": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Caesar Dressing": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Cagliostro": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Cairo": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal"], "axes": [{"tag": "slnt", "min": -11, "max": 11, "defaultValue": 0}, {"tag": "wght", "min": 200, "max": 1000, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext"]}, "Cairo Play": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal"], "axes": [{"tag": "slnt", "min": -11, "max": 11, "defaultValue": 0}, {"tag": "wght", "min": 200, "max": 1000, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext"]}, "Caladea": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Calistoga": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Calligraffitti": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Cambay": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Cambo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Candal": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Cantarell": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Cantata One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Cantora One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Caprasimo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Capriola": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Caramel": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Carattere": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Cardo": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["greek", "greek-ext", "latin", "latin-ext"]}, "Carlito": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Carme": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Carrois Gothic": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Carrois Gothic SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Carter One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Castoro": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Castoro Titling": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Catamaran": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "tamil"]}, "Caudex": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["greek", "greek-ext", "latin", "latin-ext"]}, "Caveat": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "Caveat Brush": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Cedarville Cursive": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Ceviche One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Chakra Petch": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Changa": {"weights": ["200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 800, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext"]}, "Changa One": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "Chango": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Charis SIL": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Charm": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Charmonman": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Chathura": {"weights": ["100", "300", "400", "700", "800"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Chau Philomene One": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Chela One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Chelsea Market": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Chenla": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer"]}, "Cherish": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Cherry Bomb One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Cherry Cream Soda": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Cherry Swash": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Chewy": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Chicle": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Chilanka": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "malayalam"]}, "Chivo": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Chivo Mono": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Chocolate Classical Sans": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Chokokutai": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Chonburi": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Cinzel": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Cinzel Decorative": {"weights": ["400", "700", "900"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Clicker Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Climate Crisis": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "YEAR", "min": 1979, "max": 2050, "defaultValue": 1979}], "subsets": ["latin", "latin-ext"]}, "Coda": {"weights": ["400", "800"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Codystar": {"weights": ["300", "400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Coiny": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "tamil", "vietnamese"]}, "Combo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Comfortaa": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "vietnamese"]}, "Comforter": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Comforter Brush": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Comic Neue": {"weights": ["300", "400", "700"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "Coming Soon": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Comme": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Commissioner": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "FLAR", "min": 0, "max": 100, "defaultValue": 0}, {"tag": "VOLM", "min": 0, "max": 100, "defaultValue": 0}, {"tag": "slnt", "min": -12, "max": 0, "defaultValue": 0}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "vietnamese"]}, "Concert One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Condiment": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Content": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["khmer"]}, "Contrail One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Convergence": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Cookie": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Copse": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Corben": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Corinthia": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Cormorant": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Cormorant Garamond": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Cormorant Infant": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Cormorant SC": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Cormorant Unicase": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Cormorant Upright": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Courgette": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Courier Prime": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Cousine": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "hebrew", "latin", "latin-ext", "vietnamese"]}, "Coustard": {"weights": ["400", "900"], "styles": ["normal"], "subsets": ["latin"]}, "Covered By Your Grace": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Crafty Girls": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Creepster": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Crete Round": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Crimson Pro": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Crimson Text": {"weights": ["400", "600", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Croissant One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Crushed": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Cuprum": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Cute Font": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Cutive": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Cutive Mono": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "DM Mono": {"weights": ["300", "400", "500"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "DM Sans": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "opsz", "min": 9, "max": 40, "defaultValue": 14}, {"tag": "wght", "min": 100, "max": 1000, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "DM Serif Display": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "DM Serif Text": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Dai Banna SIL": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "new-tai-lue"]}, "Damion": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Dancing Script": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Danfo": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "ELSH", "min": 0, "max": 100, "defaultValue": 0}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Dangrek": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Darker Grotesque": {"weights": ["300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Darumadrop One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "David Libre": {"weights": ["400", "500", "700"], "styles": ["normal"], "subsets": ["hebrew", "latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Dawning of a New Day": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Days One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Dekko": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Dela Gothic One": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "greek", "latin", "latin-ext", "vietnamese"]}, "Delicious Handrawn": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Delius": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Delius Swash Caps": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Delius Unicase": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Della Respira": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Denk One": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Devonshire": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Dhurjati": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Didact Gothic": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext"]}, "Diphylleia": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Diplomata": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Diplomata SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Do Hyeon": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Dokdo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Domine": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Donegal One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Dongle": {"weights": ["300", "400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Doppio One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Dorsa": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Dosis": {"weights": ["200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "DotGothic16": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Doto": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "ROND", "min": 0, "max": 100, "defaultValue": 0}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Dr Sugiyama": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Duru Sans": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "DynaPuff": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic-ext", "latin", "latin-ext"]}, "Dynalight": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "EB Garamond": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Eagle Lake": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "East Sea Dokdo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Eater": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Economica": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Eczar": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "greek", "greek-ext", "latin", "latin-ext"]}, "Edu AU VIC WA NT Arrows": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Edu AU VIC WA NT Dots": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Edu AU VIC WA NT Guides": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Edu AU VIC WA NT Hand": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Edu AU VIC WA NT Pre": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Edu NSW ACT Foundation": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin"]}, "Edu QLD Beginner": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin"]}, "Edu SA Beginner": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin"]}, "Edu TAS Beginner": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin"]}, "Edu VIC WA NT Beginner": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin"]}, "El Messiri": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["arabic", "cyrillic", "latin", "latin-ext"]}, "Electrolize": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Elsie": {"weights": ["400", "900"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Elsie Swash Caps": {"weights": ["400", "900"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Emblema One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Emilys Candy": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Encode Sans": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Encode Sans Condensed": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Encode Sans Expanded": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Encode Sans SC": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Encode Sans Semi Condensed": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Encode Sans Semi Expanded": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Engagement": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Englebert": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Enriqueta": {"weights": ["400", "500", "600", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Ephesis": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Epilogue": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Erica One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Esteban": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Estonia": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Euphoria Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Ewert": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Exo": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Exo 2": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Expletus Sans": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Explora": {"weights": ["400"], "styles": ["normal"], "subsets": ["cherokee", "latin", "latin-ext", "vietnamese"]}, "Faculty Glyphic": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Fahkwang": {"weights": ["200", "300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Familjen Grotesk": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Fanwood Text": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Farro": {"weights": ["300", "400", "500", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Farsan": {"weights": ["400"], "styles": ["normal"], "subsets": ["gujarati", "latin", "latin-ext", "vietnamese"]}, "Fascinate": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Fascinate Inline": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Faster One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Fasthand": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Fauna One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Faustina": {"weights": ["300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Federant": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Federo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Felipa": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Fenix": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Festive": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Figtree": {"weights": ["300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Finger Paint": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Finlandica": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "Fira Code": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext"]}, "Fira Mono": {"weights": ["400", "500", "700"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext"]}, "Fira Sans": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Fira Sans Condensed": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Fira Sans Extra Condensed": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Fjalla One": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Fjord One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Flamenco": {"weights": ["300", "400"], "styles": ["normal"], "subsets": ["latin"]}, "Flavors": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Fleur De Leah": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Flow Block": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Flow Circular": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Flow Rounded": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Foldit": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Fondamento": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Fontdiner Swanky": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Forum": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "Fragment Mono": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["cyrillic-ext", "latin", "latin-ext"]}, "Francois One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Frank Ruhl Libre": {"weights": ["300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 900, "defaultValue": 400}], "subsets": ["hebrew", "latin", "latin-ext"]}, "Fraunces": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "SOFT", "min": 0, "max": 100, "defaultValue": 0}, {"tag": "WONK", "min": 0, "max": 1, "defaultValue": 0}, {"tag": "opsz", "min": 9, "max": 144, "defaultValue": 14}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Freckle Face": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Fredericka the Great": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Fredoka": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["hebrew", "latin", "latin-ext"]}, "Freehand": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Freeman": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Fresca": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Frijole": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Fruktur": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Fugaz One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Fuggles": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Funnel Display": {"weights": ["300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Funnel Sans": {"weights": ["300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Fustat": {"weights": ["200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 800, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext"]}, "Fuzzy Bubbles": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "GFS Didot": {"weights": ["400"], "styles": ["normal"], "subsets": ["greek"]}, "GFS Neohellenic": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["greek"]}, "Ga Maamli": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Gabarito": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Gabriela": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "Gaegu": {"weights": ["300", "400", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Gafata": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Gajraj One": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Galada": {"weights": ["400"], "styles": ["normal"], "subsets": ["bengali", "latin"]}, "Galdeano": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Galindo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Gamja Flower": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Gantari": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Gasoek One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Gayathri": {"weights": ["100", "400", "700"], "styles": ["normal"], "subsets": ["latin", "malayalam"]}, "Geist": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Geist Mono": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Gelasio": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Gemunu Libre": {"weights": ["200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "sinhala"]}, "Genos": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cherokee", "latin", "latin-ext", "vietnamese"]}, "Gentium Book Plus": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Gentium Plus": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Geo": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "Geologica": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "CRSV", "min": 0, "max": 1, "defaultValue": 0}, {"tag": "SHRP", "min": 0, "max": 100, "defaultValue": 0}, {"tag": "slnt", "min": -12, "max": 0, "defaultValue": 0}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "vietnamese"]}, "Georama": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 62.5, "max": 150, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Geostar": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Geostar Fill": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Germania One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Gideon Roman": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Gidugu": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "telugu"]}, "Gilda Display": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Girassol": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Give You Glory": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Glass Antiqua": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Glegoo": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Gloock": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic-ext", "latin", "latin-ext"]}, "Gloria Hallelujah": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Glory": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Gluten": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "slnt", "min": -13, "max": 13, "defaultValue": 0}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Goblin One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Gochi Hand": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Goldman": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Golos Text": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "Gorditas": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Gothic A1": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Gotu": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext", "vietnamese"]}, "Goudy Bookletter 1911": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Gowun Batang": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Gowun Dodum": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Graduate": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Grand Hotel": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Grandiflora One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Grandstander": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Grape Nuts": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Gravitas One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Great Vibes": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Grechen Fuemen": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Grenze": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Grenze Gotisch": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Grey Qo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Griffy": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Gruppo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Gudea": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Gugi": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Gulzar": {"weights": ["400"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext"]}, "Gupter": {"weights": ["400", "500", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Gurajada": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "telugu"]}, "Gwendolyn": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Habibi": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Hachi Maru Pop": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Hahmlet": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Halant": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Hammersmith One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Hanalei": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Hanalei Fill": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Handjet": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "ELGR", "min": 1, "max": 2, "defaultValue": 1}, {"tag": "ELSH", "min": 0, "max": 16, "defaultValue": 2}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["arabic", "armenian", "cyrillic", "cyrillic-ext", "greek", "hebrew", "latin", "latin-ext", "vietnamese"]}, "Handlee": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Hanken Grotesk": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Hanuman": {"weights": ["100", "300", "400", "700", "900"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Happy Monkey": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Harmattan": {"weights": ["400", "500", "600", "700"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext"]}, "Headland One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Hedvig Letters Sans": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Hedvig Letters Serif": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "opsz", "min": 12, "max": 24, "defaultValue": 24}], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Heebo": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["hebrew", "latin", "latin-ext", "math", "symbols"]}, "Henny Penny": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Hepta Slab": {"weights": ["1", "100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 1, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Herr Von Muellerhoff": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Hi Melody": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Hina Mincho": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Hind": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Hind Guntur": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "telugu"]}, "Hind Madurai": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "tamil"]}, "Hind Mysuru": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["kannada", "latin", "latin-ext"]}, "Hind Siliguri": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["bengali", "latin", "latin-ext"]}, "Hind Vadodara": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["gujarati", "latin", "latin-ext"]}, "Holtwood One SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Homemade Apple": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Homenaje": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Honk": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "MORF", "min": 0, "max": 45, "defaultValue": 15}, {"tag": "SHLN", "min": 0, "max": 100, "defaultValue": 50}], "subsets": ["latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Host Grotesk": {"weights": ["300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Hubballi": {"weights": ["400"], "styles": ["normal"], "subsets": ["kannada", "latin", "latin-ext"]}, "Hubot Sans": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Hurricane": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "IBM Plex Mono": {"weights": ["100", "200", "300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "IBM Plex Sans": {"weights": ["100", "200", "300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "vietnamese"]}, "IBM Plex Sans Arabic": {"weights": ["100", "200", "300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["arabic", "cyrillic-ext", "latin", "latin-ext"]}, "IBM Plex Sans Condensed": {"weights": ["100", "200", "300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "IBM Plex Sans Devanagari": {"weights": ["100", "200", "300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["cyrillic-ext", "<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "IBM Plex Sans Hebrew": {"weights": ["100", "200", "300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "IBM Plex Sans JP": {"weights": ["100", "200", "300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "IBM Plex Sans KR": {"weights": ["100", "200", "300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "IBM Plex Sans Thai": {"weights": ["100", "200", "300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["cyrillic-ext", "latin", "latin-ext", "thai"]}, "IBM Plex Sans Thai Looped": {"weights": ["100", "200", "300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["cyrillic-ext", "latin", "latin-ext", "thai"]}, "IBM Plex Serif": {"weights": ["100", "200", "300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "IM Fell DW Pica": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "IM Fell DW Pica SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "IM Fell Double Pica": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "IM Fell Double Pica SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "IM Fell English": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "IM Fell English SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "IM Fell French Canon": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "IM Fell French Canon SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "IM Fell Great Primer": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "IM Fell Great Primer SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Ibarra Real Nova": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Iceberg": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Iceland": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Imbue": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "opsz", "min": 10, "max": 100, "defaultValue": 10}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Imperial Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Imprima": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Inclusive Sans": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Inconsolata": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 50, "max": 200, "defaultValue": 100}, {"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Inder": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Indie Flower": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Ingrid Darling": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Inika": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Inknut Antiqua": {"weights": ["300", "400", "500", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Inria Sans": {"weights": ["300", "400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Inria Serif": {"weights": ["300", "400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Inspiration": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Instrument Sans": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 75, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Instrument Serif": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Inter": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "opsz", "min": 14, "max": 32, "defaultValue": 14}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Inter Tight": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Irish Grover": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Island Moments": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Istok Web": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "Italiana": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Italianno": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Itim": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Jacquard 12": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Jacquard 12 Charted": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Jacquard 24": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Jacquard 24 Charted": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Jacquarda Bastarda 9": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Jacquarda Bastarda 9 Charted": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Jacques Francois": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Jacques Francois Shadow": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Jaini": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Jaini Purva": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Jaldi": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Jaro": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "opsz", "min": 6, "max": 72, "defaultValue": 14}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Jersey 10": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Jersey 10 Charted": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Jersey 15": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Jersey 15 Charted": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Jersey 20": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Jersey 20 Charted": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Jersey 25": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Jersey 25 Charted": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "JetBrains Mono": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "vietnamese"]}, "Jim Nightshade": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Joan": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Jockey One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Jolly Lodger": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Jomhuria": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Jomolhari": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "tibetan"]}, "Josefin Sans": {"weights": ["100", "200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Josefin Slab": {"weights": ["100", "200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 700, "defaultValue": 400}], "subsets": ["latin"]}, "Jost": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Joti One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Jua": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Judson": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Julee": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Julius Sans One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Junge": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Jura": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "kayah-li", "latin", "latin-ext", "vietnamese"]}, "Just Another Hand": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Just Me Again Down Here": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "K2D": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Kablammo": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "MORF", "min": 0, "max": 60, "defaultValue": 0}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Kadwa": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Kaisei Decol": {"weights": ["400", "500", "700"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Kaisei HarunoUmi": {"weights": ["400", "500", "700"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Kaisei Opti": {"weights": ["400", "500", "700"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Kaisei Tokumin": {"weights": ["400", "500", "700", "800"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Kalam": {"weights": ["300", "400", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Kalnia": {"weights": ["100", "200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 100, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "math"]}, "Kalnia Glaze": {"weights": ["100", "200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 100, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Kameron": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Kanit": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Kantumruy Pro": {"weights": ["100", "200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 700, "defaultValue": 400}], "subsets": ["khmer", "latin", "latin-ext"]}, "Karantina": {"weights": ["300", "400", "700"], "styles": ["normal"], "subsets": ["hebrew", "latin", "latin-ext"]}, "Karla": {"weights": ["200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 200, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Karla Tamil Inclined": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["tamil"]}, "Karla Tamil Upright": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["tamil"]}, "Karma": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Katibeh": {"weights": ["400"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext"]}, "Kaushan Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Kavivanar": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "tamil"]}, "Kavoon": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Kay Pho Du": {"weights": ["400", "500", "600", "700"], "styles": ["normal"], "subsets": ["kayah-li", "latin", "latin-ext"]}, "Kdam Thmor Pro": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer", "latin", "latin-ext"]}, "Keania One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Kelly Slab": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Kenia": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Khand": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Khmer": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer"]}, "Khula": {"weights": ["300", "400", "600", "700", "800"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Kings": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Kirang Haerang": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Kite One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Kiwi Maru": {"weights": ["300", "400", "500"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Klee One": {"weights": ["400", "600"], "styles": ["normal"], "subsets": ["cyrillic", "greek-ext", "latin", "latin-ext"]}, "Knewave": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "KoHo": {"weights": ["200", "300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Kodchasan": {"weights": ["200", "300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Kode Mono": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Koh Santepheap": {"weights": ["100", "300", "400", "700", "900"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Kolker Brush": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Konkhmer Sleokchher": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer", "latin", "latin-ext"]}, "Kosugi": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Kosugi Maru": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Kotta One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Koulen": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Kranky": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Kreon": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Kristi": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Krona One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Krub": {"weights": ["200", "300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Kufam": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext", "vietnamese"]}, "Kulim Park": {"weights": ["200", "300", "400", "600", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Kumar One": {"weights": ["400"], "styles": ["normal"], "subsets": ["gujarati", "latin", "latin-ext"]}, "Kumar One Outline": {"weights": ["400"], "styles": ["normal"], "subsets": ["gujarati", "latin", "latin-ext"]}, "Kumbh Sans": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "YOPQ", "min": 40, "max": 300, "defaultValue": 300}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Kurale": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "LXGW WenKai Mono TC": {"weights": ["300", "400", "700"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "lisu", "vietnamese"]}, "LXGW WenKai TC": {"weights": ["300", "400", "700"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "lisu", "vietnamese"]}, "La Belle Aurore": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Labrada": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Lacquer": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Laila": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Lakki Reddy": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Lalezar": {"weights": ["400"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext", "vietnamese"]}, "Lancelot": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Langar": {"weights": ["400"], "styles": ["normal"], "subsets": ["gur<PERSON><PERSON>i", "latin", "latin-ext"]}, "Lateef": {"weights": ["200", "300", "400", "500", "600", "700", "800"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext"]}, "Lato": {"weights": ["100", "300", "400", "700", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Lavishly Yours": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "League Gothic": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 100, "defaultValue": 100}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "League Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "League Spartan": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Leckerli One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Ledger": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Lekton": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Lemon": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Lemonada": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext", "vietnamese"]}, "Lexend": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Lexend Deca": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Lexend Exa": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Lexend Giga": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Lexend Mega": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Lexend Peta": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Lexend Tera": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Lexend Zetta": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Libre Barcode 128": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Libre Barcode 128 Text": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Libre Barcode 39": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Libre Barcode 39 Extended": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Libre Barcode 39 Extended Text": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Libre Barcode 39 Text": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Libre Barcode EAN13 Text": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Libre Baskerville": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Libre Bodoni": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Libre Caslon Display": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Libre Caslon Text": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Libre Franklin": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Licorice": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Life Savers": {"weights": ["400", "700", "800"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Lilita One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Lily Script One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Limelight": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Linden Hill": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Linefont": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 25, "max": 200, "defaultValue": 100}, {"tag": "wght", "min": 4, "max": 1000, "defaultValue": 400}], "subsets": []}, "Lisu Bosa": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "lisu"]}, "Literata": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "opsz", "min": 7, "max": 72, "defaultValue": 14}, {"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Liu Jian Mao Cao": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Livvic": {"weights": ["100", "200", "300", "400", "500", "600", "700", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Lobster": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Lobster Two": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "Londrina Outline": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Londrina Shadow": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Londrina Sketch": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Londrina Solid": {"weights": ["100", "300", "400", "900"], "styles": ["normal"], "subsets": ["latin"]}, "Long Cang": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Lora": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Love Light": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Love Ya Like A Sister": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Loved by the King": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Lovers Quarrel": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Luckiest Guy": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Lugrasimo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Lumanosimo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Lunasima": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "hebrew", "latin", "latin-ext", "vietnamese"]}, "Lusitana": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Lustria": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Luxurious Roman": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Luxurious Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "M PLUS 1": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "M PLUS 1 Code": {"weights": ["100", "200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "M PLUS 1p": {"weights": ["100", "300", "400", "500", "700", "800", "900"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "hebrew", "latin", "latin-ext", "vietnamese"]}, "M PLUS 2": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "M PLUS Code Latin": {"weights": ["100", "200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 100, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "M PLUS Rounded 1c": {"weights": ["100", "300", "400", "500", "700", "800", "900"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "hebrew", "latin", "latin-ext", "vietnamese"]}, "Ma Shan Zheng": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Macondo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Macondo Swash Caps": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Mada": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext"]}, "Madimi One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Magra": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Maiden Orange": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Maitree": {"weights": ["200", "300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Major Mono Display": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Mako": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Mali": {"weights": ["200", "300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Mallanna": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Maname": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "sinhala", "vietnamese"]}, "Mandali": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Manjari": {"weights": ["100", "400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "malayalam"]}, "Manrope": {"weights": ["200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 800, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "vietnamese"]}, "Mansalva": {"weights": ["400"], "styles": ["normal"], "subsets": ["greek", "latin", "latin-ext", "vietnamese"]}, "Manuale": {"weights": ["300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Marcellus": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Marcellus SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Marck Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Margarine": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Marhey": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext"]}, "Markazi Text": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext", "vietnamese"]}, "Marko One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Marmelad": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Martel": {"weights": ["200", "300", "400", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Martel Sans": {"weights": ["200", "300", "400", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Martian Mono": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 112.5, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "Marvel": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "Mate": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Mate SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Matemasie": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Maven Pro": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "McLaren": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Mea Culpa": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Meddon": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "MedievalSharp": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Medula One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Meera Inimai": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "tamil"]}, "Megrim": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Meie Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Meow Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Merienda": {"weights": ["300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Merriweather": {"weights": ["300", "400", "700", "900"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Merriweather Sans": {"weights": ["300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 800, "defaultValue": 400}], "subsets": ["cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Metal": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Metal Mania": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Metamorphous": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Metrophobic": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Michroma": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Micro 5": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Micro 5 Charted": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Milonga": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Miltonian": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Miltonian Tattoo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Mina": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["bengali", "latin", "latin-ext"]}, "Mingzat": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "lepcha"]}, "Miniver": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Miriam Libre": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["hebrew", "latin", "latin-ext"]}, "Mirza": {"weights": ["400", "500", "600", "700"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext"]}, "Miss Fajardose": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Mitr": {"weights": ["200", "300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Mochiy Pop One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Mochiy Pop P One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Modak": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Modern Antiqua": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Moderustic": {"weights": ["300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 800, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext"]}, "Mogra": {"weights": ["400"], "styles": ["normal"], "subsets": ["gujarati", "latin", "latin-ext"]}, "Mohave": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Moirai One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Molengo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Molle": {"weights": ["400"], "styles": ["italic"], "subsets": ["latin", "latin-ext"]}, "Mona Sans": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Monda": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Monofett": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Monomaniac One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Monoton": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Monsieur La Doulaise": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Montaga": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Montagu Slab": {"weights": ["100", "200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "opsz", "min": 16, "max": 144, "defaultValue": 144}, {"tag": "wght", "min": 100, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "MonteCarlo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Montez": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Montserrat": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Montserrat Alternates": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Montserrat Subrayada": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Montserrat Underline": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Moo Lah Lah": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Mooli": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Moon Dance": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Moul": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Moulpali": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Mountains of Christmas": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Mouse Memoirs": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Mr Bedfort": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Mr Dafoe": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Mr De Haviland": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Mrs Saint Delafield": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Mrs Sheppards": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Ms Madi": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Mukta": {"weights": ["200", "300", "400", "500", "600", "700", "800"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Mukta Mahee": {"weights": ["200", "300", "400", "500", "600", "700", "800"], "styles": ["normal"], "subsets": ["gur<PERSON><PERSON>i", "latin", "latin-ext"]}, "Mukta Malar": {"weights": ["200", "300", "400", "500", "600", "700", "800"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "tamil"]}, "Mukta Vaani": {"weights": ["200", "300", "400", "500", "600", "700", "800"], "styles": ["normal"], "subsets": ["gujarati", "latin", "latin-ext"]}, "Mulish": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 200, "max": 1000, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Murecho": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext"]}, "MuseoModerno": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "My Soul": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Mynerve": {"weights": ["400"], "styles": ["normal"], "subsets": ["greek", "latin", "latin-ext", "vietnamese"]}, "Mystery Quest": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "NTR": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Nabla": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "EDPT", "min": 0, "max": 200, "defaultValue": 100}, {"tag": "EHLT", "min": 0, "max": 24, "defaultValue": 12}], "subsets": ["cyrillic-ext", "latin", "latin-ext", "math", "vietnamese"]}, "Namdhinggo": {"weights": ["400", "500", "600", "700", "800"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "limbu"]}, "Nanum Brush Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Nanum Gothic": {"weights": ["400", "700", "800"], "styles": ["normal"], "subsets": ["latin"]}, "Nanum Gothic Coding": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Nanum Myeongjo": {"weights": ["400", "700", "800"], "styles": ["normal"], "subsets": ["latin"]}, "Nanum Pen Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Narnoor": {"weights": ["400", "500", "600", "700", "800"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON>-gondi", "latin", "latin-ext", "math", "symbols"]}, "Neonderthaw": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Nerko One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Neucha": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin"]}, "Neuton": {"weights": ["200", "300", "400", "700", "800"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "New Amsterdam": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "New Rocker": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "New Tegomin": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "News Cycle": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Newsreader": {"weights": ["200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "opsz", "min": 6, "max": 72, "defaultValue": 16}, {"tag": "wght", "min": 200, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Niconne": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Niramit": {"weights": ["200", "300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Nixie One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Nobile": {"weights": ["400", "500", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Nokora": {"weights": ["100", "300", "400", "700", "900"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Norican": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Nosifer": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Notable": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Nothing You Could Do": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Noticia Text": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Noto Color Emoji": {"weights": ["400"], "styles": ["normal"], "subsets": ["emoji"]}, "Noto Emoji": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["emoji"]}, "Noto Kufi Arabic": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext", "math", "symbols"]}, "Noto Music": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "music"]}, "Noto Naskh Arabic": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext", "math", "symbols"]}, "Noto Nastaliq Urdu": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext"]}, "Noto Rashi Hebrew": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["greek-ext", "hebrew", "latin", "latin-ext"]}, "Noto Sans": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "<PERSON><PERSON><PERSON><PERSON>", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Noto Sans Adlam": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["adlam", "latin", "latin-ext"]}, "Noto Sans Adlam Unjoined": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["adlam", "latin", "latin-ext"]}, "Noto Sans Anatolian Hieroglyphs": {"weights": ["400"], "styles": ["normal"], "subsets": ["anatolian-hieroglyphs", "latin", "latin-ext"]}, "Noto Sans Arabic": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["arabic"]}, "Noto Sans Armenian": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["armenian", "latin", "latin-ext"]}, "Noto Sans Avestan": {"weights": ["400"], "styles": ["normal"], "subsets": ["a<PERSON><PERSON>", "latin", "latin-ext"]}, "Noto Sans Balinese": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["balinese", "latin", "latin-ext"]}, "Noto Sans Bamum": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["bamum", "latin", "latin-ext"]}, "Noto Sans Bassa Vah": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["bassa-vah", "latin", "latin-ext"]}, "Noto Sans Batak": {"weights": ["400"], "styles": ["normal"], "subsets": ["batak", "latin", "latin-ext"]}, "Noto Sans Bengali": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["bengali", "latin", "latin-ext"]}, "Noto Sans Bhaiksuki": {"weights": ["400"], "styles": ["normal"], "subsets": ["b<PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Noto Sans Brahmi": {"weights": ["400"], "styles": ["normal"], "subsets": ["brahmi", "latin", "latin-ext", "math", "symbols"]}, "Noto Sans Buginese": {"weights": ["400"], "styles": ["normal"], "subsets": ["buginese", "latin", "latin-ext"]}, "Noto Sans Buhid": {"weights": ["400"], "styles": ["normal"], "subsets": ["buhid", "latin", "latin-ext"]}, "Noto Sans Canadian Aboriginal": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["canadian-aboriginal", "latin", "latin-ext", "math", "symbols"]}, "Noto Sans Carian": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON>ian", "latin", "latin-ext"]}, "Noto Sans Caucasian Albanian": {"weights": ["400"], "styles": ["normal"], "subsets": ["caucasian-albanian", "latin", "latin-ext"]}, "Noto Sans Chakma": {"weights": ["400"], "styles": ["normal"], "subsets": ["chakma", "latin", "latin-ext"]}, "Noto Sans Cham": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cham", "latin", "latin-ext"]}, "Noto Sans Cherokee": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cherokee", "latin", "latin-ext"]}, "Noto Sans Chorasmian": {"weights": ["400"], "styles": ["normal"], "subsets": ["chorasmian", "latin", "latin-ext", "math", "symbols"]}, "Noto Sans Coptic": {"weights": ["400"], "styles": ["normal"], "subsets": ["cop<PERSON>", "latin", "latin-ext"]}, "Noto Sans Cuneiform": {"weights": ["400"], "styles": ["normal"], "subsets": ["cuneiform", "latin", "latin-ext"]}, "Noto Sans Cypriot": {"weights": ["400"], "styles": ["normal"], "subsets": ["cypriot", "latin", "latin-ext"]}, "Noto Sans Cypro Minoan": {"weights": ["400"], "styles": ["normal"], "subsets": ["cypro-minoan", "latin", "latin-ext"]}, "Noto Sans Deseret": {"weights": ["400"], "styles": ["normal"], "subsets": ["deseret", "latin", "latin-ext"]}, "Noto Sans Devanagari": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Noto Sans Display": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Noto Sans Duployan": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["duployan", "latin", "latin-ext"]}, "Noto Sans Egyptian Hieroglyphs": {"weights": ["400"], "styles": ["normal"], "subsets": ["egyptian-hieroglyphs", "latin", "latin-ext"]}, "Noto Sans Elbasan": {"weights": ["400"], "styles": ["normal"], "subsets": ["elbasan", "latin", "latin-ext"]}, "Noto Sans Elymaic": {"weights": ["400"], "styles": ["normal"], "subsets": ["elymaic", "latin", "latin-ext"]}, "Noto Sans Ethiopic": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["ethiopic", "latin", "latin-ext"]}, "Noto Sans Georgian": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic-ext", "georgian", "greek-ext", "latin", "latin-ext", "math", "symbols"]}, "Noto Sans Glagolitic": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic-ext", "glagolitic", "latin", "latin-ext", "math", "symbols"]}, "Noto Sans Gothic": {"weights": ["400"], "styles": ["normal"], "subsets": ["gothic", "latin", "latin-ext"]}, "Noto Sans Grantha": {"weights": ["400"], "styles": ["normal"], "subsets": ["grantha", "latin", "latin-ext"]}, "Noto Sans Gujarati": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["gujarati", "latin", "latin-ext", "math", "symbols"]}, "Noto Sans Gunjala Gondi": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["<PERSON><PERSON><PERSON>-gondi", "latin", "latin-ext"]}, "Noto Sans Gurmukhi": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["gur<PERSON><PERSON>i", "latin", "latin-ext"]}, "Noto Sans HK": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Noto Sans Hanifi Rohingya": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["hanifi-rohingya", "latin", "latin-ext"]}, "Noto Sans Hanunoo": {"weights": ["400"], "styles": ["normal"], "subsets": ["hanunoo", "latin", "latin-ext"]}, "Noto Sans Hatran": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON>ran", "latin", "latin-ext"]}, "Noto Sans Hebrew": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic-ext", "greek-ext", "hebrew", "latin", "latin-ext"]}, "Noto Sans Imperial Aramaic": {"weights": ["400"], "styles": ["normal"], "subsets": ["imperial-aramaic", "latin", "latin-ext"]}, "Noto Sans Indic Siyaq Numbers": {"weights": ["400"], "styles": ["normal"], "subsets": ["indic-siyaq-numbers", "latin", "latin-ext"]}, "Noto Sans Inscriptional Pahlavi": {"weights": ["400"], "styles": ["normal"], "subsets": ["inscriptional-pahlavi", "latin", "latin-ext"]}, "Noto Sans Inscriptional Parthian": {"weights": ["400"], "styles": ["normal"], "subsets": ["inscriptional-parthian", "latin", "latin-ext"]}, "Noto Sans JP": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Noto Sans Javanese": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["javanese", "latin", "latin-ext"]}, "Noto Sans KR": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Noto Sans Kaithi": {"weights": ["400"], "styles": ["normal"], "subsets": ["kaithi", "latin", "latin-ext"]}, "Noto Sans Kannada": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["kannada", "latin", "latin-ext"]}, "Noto Sans Kawi": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["kawi", "latin", "latin-ext"]}, "Noto Sans Kayah Li": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["kayah-li", "latin", "latin-ext"]}, "Noto Sans Kharoshthi": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Noto Sans Khmer": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["khmer", "latin", "latin-ext"]}, "Noto Sans Khojki": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Noto Sans Khudawadi": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Noto Sans Lao": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["lao", "latin", "latin-ext"]}, "Noto Sans Lao Looped": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["lao", "latin", "latin-ext"]}, "Noto Sans Lepcha": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "lepcha"]}, "Noto Sans Limbu": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "limbu"]}, "Noto Sans Linear A": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "linear-a"]}, "Noto Sans Linear B": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "linear-b"]}, "Noto Sans Lisu": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "lisu"]}, "Noto Sans Lycian": {"weights": ["400"], "styles": ["normal"], "subsets": ["lycian"]}, "Noto Sans Lydian": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "lydian"]}, "Noto Sans Mahajani": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "<PERSON><PERSON><PERSON><PERSON>"]}, "Noto Sans Malayalam": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "malayalam"]}, "Noto Sans Mandaic": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "mandaic"]}, "Noto Sans Manichaean": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "manichaean"]}, "Noto Sans Marchen": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "marchen"]}, "Noto Sans Masaram Gondi": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "masar<PERSON>-gondi"]}, "Noto Sans Math": {"weights": ["400"], "styles": ["normal"], "subsets": ["math"]}, "Noto Sans Mayan Numerals": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "mayan-numerals"]}, "Noto Sans Medefaidrin": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "medefaidrin"]}, "Noto Sans Meetei Mayek": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "<PERSON><PERSON>-may<PERSON>"]}, "Noto Sans Mende Kikakui": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "mende-kikakui"]}, "Noto Sans Meroitic": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "meroitic", "meroitic-cursive", "meroitic-hieroglyphs"]}, "Noto Sans Miao": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "miao"]}, "Noto Sans Modi": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "modi"]}, "Noto Sans Mongolian": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "mongolian", "symbols"]}, "Noto Sans Mono": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Noto Sans Mro": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "mro"]}, "Noto Sans Multani": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "multani"]}, "Noto Sans Myanmar": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["myanmar"]}, "Noto Sans NKo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "nko"]}, "Noto Sans NKo Unjoined": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "nko"]}, "Noto Sans Nabataean": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "nabataean"]}, "Noto Sans Nag Mundari": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "nag-mundari"]}, "Noto Sans Nandinagari": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "nandinagari"]}, "Noto Sans New Tai Lue": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "new-tai-lue"]}, "Noto Sans Newa": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "newa"]}, "Noto Sans Nushu": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "nushu"]}, "Noto Sans Ogham": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "ogham"]}, "Noto Sans Ol Chiki": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "ol-chiki"]}, "Noto Sans Old Hungarian": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "old-hungarian"]}, "Noto Sans Old Italic": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "old-italic"]}, "Noto Sans Old North Arabian": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "old-north-arabian"]}, "Noto Sans Old Permic": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic-ext", "latin", "latin-ext", "old-permic"]}, "Noto Sans Old Persian": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "old-persian"]}, "Noto Sans Old Sogdian": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "old-sogdian"]}, "Noto Sans Old South Arabian": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "old-south-arabian"]}, "Noto Sans Old Turkic": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "old-turkic"]}, "Noto Sans Oriya": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "oriya"]}, "Noto Sans Osage": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "osage"]}, "Noto Sans Osmanya": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "osmanya"]}, "Noto Sans Pahawh Hmong": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "pahawh-hmong"]}, "Noto Sans Palmyrene": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "palmyrene"]}, "Noto Sans Pau Cin Hau": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "pau-cin-hau"]}, "Noto Sans PhagsPa": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "phags-pa", "symbols"]}, "Noto Sans Phoenician": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "phoenician"]}, "Noto Sans Psalter Pahlavi": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "psalter-pahlavi"]}, "Noto Sans Rejang": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "rejang"]}, "Noto Sans Runic": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "runic"]}, "Noto Sans SC": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Noto Sans Samaritan": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "samaritan"]}, "Noto Sans Saurashtra": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "saurashtra"]}, "Noto Sans Sharada": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "sharada"]}, "Noto Sans Shavian": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "shavian"]}, "Noto Sans Siddham": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "siddham"]}, "Noto Sans SignWriting": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "signwriting"]}, "Noto Sans Sinhala": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "sinhala"]}, "Noto Sans Sogdian": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "sogdian"]}, "Noto Sans Sora Sompeng": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "sora-sompeng"]}, "Noto Sans Soyombo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "soyombo"]}, "Noto Sans Sundanese": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "sundanese"]}, "Noto Sans Syloti Nagri": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "syloti-nagri"]}, "Noto Sans Symbols": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "symbols"]}, "Noto Sans Symbols 2": {"weights": ["400"], "styles": ["normal"], "subsets": ["braille", "latin", "latin-ext", "math", "mayan-numerals", "symbols"]}, "Noto Sans Syriac": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "syriac"]}, "Noto Sans Syriac Eastern": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "syriac"]}, "Noto Sans TC": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Noto Sans Tagalog": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "tagalog"]}, "Noto Sans Tagbanwa": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "tagbanwa"]}, "Noto Sans Tai Le": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "tai-le"]}, "Noto Sans Tai Tham": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "tai-tham"]}, "Noto Sans Tai Viet": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "tai-viet"]}, "Noto Sans Takri": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "takri"]}, "Noto Sans Tamil": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "tamil"]}, "Noto Sans Tamil Supplement": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "tamil-supplement"]}, "Noto Sans Tangsa": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "tangsa"]}, "Noto Sans Telugu": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "telugu"]}, "Noto Sans Thaana": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "thaana"]}, "Noto Sans Thai": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "thai"]}, "Noto Sans Thai Looped": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "thai"]}, "Noto Sans Tifinagh": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "tif<PERSON><PERSON>"]}, "Noto Sans Tirhuta": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "tir<PERSON><PERSON>"]}, "Noto Sans Ugaritic": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "ugaritic"]}, "Noto Sans Vai": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vai"]}, "Noto Sans Vithkuqi": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vith<PERSON><PERSON>"]}, "Noto Sans Wancho": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "wancho"]}, "Noto Sans Warang Citi": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "warang-citi"]}, "Noto Sans Yi": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "yi"]}, "Noto Sans Zanabazar Square": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "zanabazar-square"]}, "Noto Serif": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Noto Serif Ahom": {"weights": ["400"], "styles": ["normal"], "subsets": ["ahom", "latin", "latin-ext"]}, "Noto Serif Armenian": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["armenian", "latin", "latin-ext"]}, "Noto Serif Balinese": {"weights": ["400"], "styles": ["normal"], "subsets": ["balinese", "latin", "latin-ext"]}, "Noto Serif Bengali": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["bengali", "latin", "latin-ext"]}, "Noto Serif Devanagari": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Noto Serif Display": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Noto Serif Dogra": {"weights": ["400"], "styles": ["normal"], "subsets": ["dogra", "latin", "latin-ext"]}, "Noto Serif Ethiopic": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["ethiopic", "latin", "latin-ext"]}, "Noto Serif Georgian": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["georgian", "latin", "latin-ext"]}, "Noto Serif Grantha": {"weights": ["400"], "styles": ["normal"], "subsets": ["grantha", "latin", "latin-ext"]}, "Noto Serif Gujarati": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["gujarati", "latin", "latin-ext", "math", "symbols"]}, "Noto Serif Gurmukhi": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["gur<PERSON><PERSON>i", "latin", "latin-ext"]}, "Noto Serif HK": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Noto Serif Hebrew": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["hebrew", "latin", "latin-ext"]}, "Noto Serif JP": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Noto Serif KR": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Noto Serif Kannada": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["kannada", "latin", "latin-ext"]}, "Noto Serif Khitan Small Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["khitan-small-script", "latin", "latin-ext"]}, "Noto Serif Khmer": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["khmer", "latin", "latin-ext"]}, "Noto Serif Khojki": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Noto Serif Lao": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["lao", "latin", "latin-ext"]}, "Noto Serif Makasar": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "makasar"]}, "Noto Serif Malayalam": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "malayalam"]}, "Noto Serif Myanmar": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["myanmar"]}, "Noto Serif NP Hmong": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "nyiakeng-puachue-hmong"]}, "Noto Serif Old Uyghur": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "old-uyghur"]}, "Noto Serif Oriya": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "oriya"]}, "Noto Serif Ottoman Siyaq": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "ottoman-siyaq-numbers"]}, "Noto Serif SC": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Noto Serif Sinhala": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "sinhala"]}, "Noto Serif TC": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Noto Serif Tamil": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "tamil"]}, "Noto Serif Tangut": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "tangut"]}, "Noto Serif Telugu": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "telugu"]}, "Noto Serif Thai": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 62.5, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "thai"]}, "Noto Serif Tibetan": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "tibetan"]}, "Noto Serif Toto": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "toto"]}, "Noto Serif Vithkuqi": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vith<PERSON><PERSON>"]}, "Noto Serif Yezidi": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "<PERSON><PERSON><PERSON>"]}, "Noto Traditional Nushu": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "nushu"]}, "Noto Znamenny Musical Notation": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols", "znamenny"]}, "Nova Cut": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Nova Flat": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Nova Mono": {"weights": ["400"], "styles": ["normal"], "subsets": ["greek", "latin", "latin-ext"]}, "Nova Oval": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Nova Round": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Nova Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Nova Slim": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Nova Square": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Numans": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Nunito": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 200, "max": 1000, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Nunito Sans": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "YTLC", "min": 440, "max": 540, "defaultValue": 500}, {"tag": "opsz", "min": 6, "max": 12, "defaultValue": 12}, {"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 200, "max": 1000, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Nuosu SIL": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "yi"]}, "Odibee Sans": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Odor Mean Chey": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Offside": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Oi": {"weights": ["400"], "styles": ["normal"], "subsets": ["arabic", "cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "tamil", "vietnamese"]}, "Ojuju": {"weights": ["200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Old Standard TT": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Oldenburg": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Ole": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Oleo Script": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Oleo Script Swash Caps": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Onest": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "Oooh Baby": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Open Sans": {"weights": ["300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 75, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 300, "max": 800, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "hebrew", "latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Oranienbaum": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "Orbit": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Orbitron": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["latin"]}, "Oregano": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Orelega One": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "Orienta": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Original Surfer": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Oswald": {"weights": ["200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Outfit": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Over the Rainbow": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Overlock": {"weights": ["400", "700", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Overlock SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Overpass": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Overpass Mono": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Ovo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Oxanium": {"weights": ["200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Oxygen": {"weights": ["300", "400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Oxygen Mono": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "PT Mono": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "PT Sans": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "PT Sans Caption": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "PT Sans Narrow": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "PT Serif": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "PT Serif Caption": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "Pacifico": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Padauk": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "myanmar"]}, "Padyakke Expanded One": {"weights": ["400"], "styles": ["normal"], "subsets": ["kannada", "latin", "latin-ext"]}, "Palanquin": {"weights": ["100", "200", "300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Palanquin Dark": {"weights": ["400", "500", "600", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Palette Mosaic": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Pangolin": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Paprika": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Parisienne": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Parkinsans": {"weights": ["300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Passero One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Passion One": {"weights": ["400", "700", "900"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Passions Conflict": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Pathway Extreme": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "opsz", "min": 8, "max": 144, "defaultValue": 12}, {"tag": "wdth", "min": 75, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Pathway Gothic One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Patrick Hand": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Patrick Hand SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Pattaya": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext", "thai", "vietnamese"]}, "Patua One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Pavanam": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "tamil"]}, "Paytone One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Peddana": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Peralta": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Permanent Marker": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Petemoss": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Petit Formal Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Petrona": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Phetsarath": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["lao"]}, "Philosopher": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Phudu": {"weights": ["300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Piazzolla": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "opsz", "min": 8, "max": 30, "defaultValue": 14}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Piedra": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Pinyon Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Pirata One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Pixelify Sans": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Plaster": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Platypi": {"weights": ["300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Play": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "vietnamese"]}, "Playball": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Playfair": {"weights": ["300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "opsz", "min": 5, "max": 1200, "defaultValue": 14}, {"tag": "wdth", "min": 87.5, "max": 112.5, "defaultValue": 100}, {"tag": "wght", "min": 300, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Playfair Display": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Playfair Display SC": {"weights": ["400", "700", "900"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Playpen Sans": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["emoji", "latin", "latin-ext", "math", "vietnamese"]}, "Playwrite AR": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite AR Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite AT": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite AT Guides": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": []}, "Playwrite AU NSW": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite AU NSW Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite AU QLD": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite AU QLD Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite AU SA": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite AU SA Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite AU TAS": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite AU TAS Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite AU VIC": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite AU VIC Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite BE VLG": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite BE VLG Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite BE WAL": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite BE WAL Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite BR": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite BR Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite CA": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite CA Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite CL": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite CL Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite CO": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite CO Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite CU": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite CU Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite CZ": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite CZ Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite DE Grund": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite DE Grund Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite DE LA": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite DE LA Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite DE SAS": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite DE SAS Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite DE VA": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite DE VA Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite DK Loopet": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite DK Loopet Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite DK Uloopet": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite DK Uloopet Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite ES": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite ES Deco": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite ES Deco Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite ES Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite FR Moderne": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite FR Moderne Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite FR Trad": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite FR Trad Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite GB J": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite GB J Guides": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": []}, "Playwrite GB S": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite GB S Guides": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": []}, "Playwrite HR": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite HR Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite HR Lijeva": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite HR Lijeva Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite HU": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite HU Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite ID": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite ID Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite IE": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite IE Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite IN": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite IN Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite IS": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite IS Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite IT Moderna": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite IT Moderna Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite IT Trad": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite IT Trad Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite MX": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite MX Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite NG Modern": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite NG Modern Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite NL": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite NL Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite NO": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite NO Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite NZ": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite NZ Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite PE": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite PE Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite PL": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite PL Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite PT": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite PT Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite RO": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite RO Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite SK": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite SK Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite TZ": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite TZ Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite US Modern": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite US Modern Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite US Trad": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite US Trad Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite VN": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite VN Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Playwrite ZA": {"weights": ["100", "200", "300", "400", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 400, "defaultValue": 400}], "subsets": []}, "Playwrite ZA Guides": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Plus Jakarta Sans": {"weights": ["200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 200, "max": 800, "defaultValue": 400}], "subsets": ["cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Podkova": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Poetsen One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Poiret One": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Poller One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Poltawski Nowy": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Poly": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Pompiere": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Ponnala": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Pontano Sans": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Poor Story": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Poppins": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Port Lligat Sans": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Port Lligat Slab": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Potta One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Pragati Narrow": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Praise": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Prata": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "vietnamese"]}, "Preahvihear": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Press Start 2P": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext"]}, "Pridi": {"weights": ["200", "300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Princess Sofia": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Prociono": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Prompt": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Prosto One": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Protest Guerrilla": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Protest Revolution": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Protest Riot": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Protest Strike": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Proza Libre": {"weights": ["400", "500", "600", "700", "800"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Public Sans": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Puppies Play": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Puritan": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "Purple Purse": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Qahiri": {"weights": ["400"], "styles": ["normal"], "subsets": ["arabic", "latin"]}, "Quando": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Quantico": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "Quattrocento": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Quattrocento Sans": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Questrial": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Quicksand": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Quintessential": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Qwigley": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Qwitcher Grypen": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "REM": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Racing Sans One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Radio Canada": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 75, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["canadian-aboriginal", "latin", "latin-ext", "vietnamese"]}, "Radio Canada Big": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Radley": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Rajdhani": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Rakkas": {"weights": ["400"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext"]}, "Raleway": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Raleway Dots": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Ramabhadra": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Ramaraja": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Rambla": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Rammetto One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Rampart One": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Ranchers": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Rancho": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Ranga": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Rasa": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["gujarati", "latin", "latin-ext", "vietnamese"]}, "Rationale": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Ravi Prakash": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Readex Pro": {"weights": ["200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "HEXP", "min": 0, "max": 100, "defaultValue": 0}, {"tag": "wght", "min": 160, "max": 700, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext", "vietnamese"]}, "Recursive": {"weights": ["300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal"], "axes": [{"tag": "CASL", "min": 0, "max": 1, "defaultValue": 0}, {"tag": "CRSV", "min": 0, "max": 1, "defaultValue": 0.5}, {"tag": "MONO", "min": 0, "max": 1, "defaultValue": 0}, {"tag": "slnt", "min": -15, "max": 0, "defaultValue": 0}, {"tag": "wght", "min": 300, "max": 1000, "defaultValue": 400}], "subsets": ["cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Red Hat Display": {"weights": ["300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Red Hat Mono": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Red Hat Text": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Red Rose": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Redacted": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Redacted Script": {"weights": ["300", "400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Reddit Mono": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Reddit Sans": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Reddit Sans Condensed": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Redressed": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Reem Kufi": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext", "vietnamese"]}, "Reem Kufi Fun": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext", "vietnamese"]}, "Reem Kufi Ink": {"weights": ["400"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext", "vietnamese"]}, "Reenie Beanie": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Reggae One": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Rethink Sans": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Revalia": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Rhodium Libre": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Ribeye": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Ribeye Marrow": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Righteous": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Risque": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Road Rage": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Roboto": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 75, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Roboto Condensed": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Roboto Flex": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal"], "axes": [{"tag": "GRAD", "min": -200, "max": 150, "defaultValue": 0}, {"tag": "XOPQ", "min": 27, "max": 175, "defaultValue": 96}, {"tag": "XTRA", "min": 323, "max": 603, "defaultValue": 468}, {"tag": "YOPQ", "min": 25, "max": 135, "defaultValue": 79}, {"tag": "YTAS", "min": 649, "max": 854, "defaultValue": 750}, {"tag": "YTDE", "min": -305, "max": -98, "defaultValue": -203}, {"tag": "YTFI", "min": 560, "max": 788, "defaultValue": 738}, {"tag": "YTLC", "min": 416, "max": 570, "defaultValue": 514}, {"tag": "YTUC", "min": 528, "max": 760, "defaultValue": 712}, {"tag": "opsz", "min": 8, "max": 144, "defaultValue": 14}, {"tag": "slnt", "min": -10, "max": 0, "defaultValue": 0}, {"tag": "wdth", "min": 25, "max": 151, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 1000, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "vietnamese"]}, "Roboto Mono": {"weights": ["100", "200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "vietnamese"]}, "Roboto Serif": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "GRAD", "min": -50, "max": 100, "defaultValue": 0}, {"tag": "opsz", "min": 8, "max": 144, "defaultValue": 14}, {"tag": "wdth", "min": 50, "max": 150, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Roboto Slab": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Rochester": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Rock 3D": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Rock Salt": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "RocknRoll One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Rokkitt": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Romanesco": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Ropa Sans": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Rosario": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Rosarivo": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Rouge Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Rowdies": {"weights": ["300", "400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Rozha One": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Rubik": {"weights": ["300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 900, "defaultValue": 400}], "subsets": ["arabic", "cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik 80s Fade": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Beastly": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Broken Fax": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext", "math", "symbols"]}, "Rubik Bubbles": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Burned": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Dirt": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Distressed": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Doodle Shadow": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext", "math", "symbols"]}, "Rubik Doodle Triangles": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext", "math", "symbols"]}, "Rubik Gemstones": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Glitch": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Glitch Pop": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext", "math", "symbols"]}, "Rubik Iso": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Lines": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext", "math", "symbols"]}, "Rubik Maps": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext", "math", "symbols"]}, "Rubik Marker Hatch": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Maze": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Microbe": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Mono One": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Rubik Moonrocks": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Pixels": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Puddles": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Scribble": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext", "math", "symbols"]}, "Rubik Spray Paint": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Storm": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Vinyl": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Rubik Wet Paint": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "hebrew", "latin", "latin-ext"]}, "Ruda": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Rufina": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Ruge Boogie": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Ruluko": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Rum Raisin": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Ruslan Display": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext", "math", "symbols"]}, "Russo One": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Ruthie": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Ruwudu": {"weights": ["400", "500", "600", "700"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext"]}, "Rye": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "STIX Two Text": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "vietnamese"]}, "SUSE": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Sacramento": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Sahitya": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Sail": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Saira": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 50, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Saira Condensed": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Saira Extra Condensed": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Saira Semi Condensed": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Saira Stencil One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Salsa": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Sanchez": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Sancreek": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Sankofa Display": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Sansita": {"weights": ["400", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Sansita Swashed": {"weights": ["300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Sarabun": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Sarala": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Sarina": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Sarpanch": {"weights": ["400", "500", "600", "700", "800", "900"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Sassy Frass": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Satisfy": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Sawarabi Gothic": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Sawarabi Mincho": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Scada": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext"]}, "Scheherazade New": {"weights": ["400", "500", "600", "700"], "styles": ["normal"], "subsets": ["arabic", "latin", "latin-ext"]}, "Schibsted Grotesk": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Schoolbell": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Scope One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Seaweed Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Secular One": {"weights": ["400"], "styles": ["normal"], "subsets": ["hebrew", "latin", "latin-ext"]}, "Sedan": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Sedan SC": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Sedgwick Ave": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Sedgwick Ave Display": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Sen": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Send Flowers": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Sevillana": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Seymour One": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Shadows Into Light": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Shadows Into Light Two": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Shalimar": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Shantell Sans": {"weights": ["300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "BNCE", "min": -100, "max": 100, "defaultValue": 0}, {"tag": "INFM", "min": 0, "max": 100, "defaultValue": 0}, {"tag": "SPAC", "min": 0, "max": 100, "defaultValue": 0}, {"tag": "wght", "min": 300, "max": 800, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Shanti": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Share": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Share Tech": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Share Tech Mono": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Shippori Antique": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Shippori Antique B1": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Shippori Mincho": {"weights": ["400", "500", "600", "700", "800"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Shippori Mincho B1": {"weights": ["400", "500", "600", "700", "800"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Shizuru": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Shojumaru": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Short Stack": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Shrikhand": {"weights": ["400"], "styles": ["normal"], "subsets": ["gujarati", "latin", "latin-ext"]}, "Siemreap": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer"]}, "Sigmar": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Sigmar One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Signika": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "GRAD", "min": -30, "max": 0, "defaultValue": 0}, {"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Signika Negative": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Silkscreen": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Simonetta": {"weights": ["400", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Single Day": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Sintony": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Sirin Stencil": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Six Caps": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Sixtyfour": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "BLED", "min": 0, "max": 100, "defaultValue": 0}, {"tag": "SCAN", "min": -53, "max": 100, "defaultValue": 0}], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Sixtyfour Convergence": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "BLED", "min": 0, "max": 100, "defaultValue": 0}, {"tag": "SCAN", "min": -53, "max": 100, "defaultValue": 0}, {"tag": "XELA", "min": -100, "max": 100, "defaultValue": 0}, {"tag": "YELA", "min": -100, "max": 100, "defaultValue": 0}], "subsets": ["latin", "latin-ext", "math", "symbols"]}, "Skranji": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Slabo 13px": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Slabo 27px": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Slackey": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Slackside One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Smokum": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Smooch": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Smooch Sans": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Smythe": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Sniglet": {"weights": ["400", "800"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Snippet": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Snowburst One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Sofadi One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Sofia": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Sofia Sans": {"weights": ["1", "100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 1, "max": 1000, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext"]}, "Sofia Sans Condensed": {"weights": ["1", "100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 1, "max": 1000, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext"]}, "Sofia Sans Extra Condensed": {"weights": ["1", "100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 1, "max": 1000, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext"]}, "Sofia Sans Semi Condensed": {"weights": ["1", "100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 1, "max": 1000, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext"]}, "Solitreo": {"weights": ["400"], "styles": ["normal"], "subsets": ["hebrew", "latin", "latin-ext"]}, "Solway": {"weights": ["300", "400", "500", "700", "800"], "styles": ["normal"], "subsets": ["latin"]}, "Sometype Mono": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Song Myung": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Sono": {"weights": ["200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "MONO", "min": 0, "max": 1, "defaultValue": 1}, {"tag": "wght", "min": 200, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Sonsie One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Sora": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Sorts Mill Goudy": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Sour Gummy": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 100, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Source Code Pro": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Source Sans 3": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext", "vietnamese"]}, "Source Serif 4": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "opsz", "min": 8, "max": 60, "defaultValue": 14}, {"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "vietnamese"]}, "Space Grotesk": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Space Mono": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Special Elite": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Spectral": {"weights": ["200", "300", "400", "500", "600", "700", "800"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Spectral SC": {"weights": ["200", "300", "400", "500", "600", "700", "800"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Spicy Rice": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Spinnaker": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Spirax": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Splash": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Spline Sans": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Spline Sans Mono": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Squada One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Square Peg": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Sree Krushnadevaraya": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Sriracha": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Srisakdi": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Staatliches": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Stalemate": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Stalinist One": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Stardos Stencil": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Stick": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Stick No Bills": {"weights": ["200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "sinhala"]}, "Stint Ultra Condensed": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Stint Ultra Expanded": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Stoke": {"weights": ["300", "400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Strait": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Style Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Stylish": {"weights": ["400"], "styles": ["normal"], "subsets": []}, "Sue Ellen Francisco": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Suez One": {"weights": ["400"], "styles": ["normal"], "subsets": ["hebrew", "latin", "latin-ext"]}, "Sulphur Point": {"weights": ["300", "400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Sumana": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Sunflower": {"weights": ["300", "500", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Sunshiney": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Supermercado One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Sura": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Suranna": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Suravaram": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Suwannaphum": {"weights": ["100", "300", "400", "700", "900"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Swanky and Moo Moo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Syncopate": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Syne": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["greek", "latin", "latin-ext"]}, "Syne Mono": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Syne Tactile": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Tac One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Tai Heritage Pro": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "tai-viet", "vietnamese"]}, "Tajawal": {"weights": ["200", "300", "400", "500", "700", "800", "900"], "styles": ["normal"], "subsets": ["arabic", "latin"]}, "Tangerine": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Tapestry": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Taprom": {"weights": ["400"], "styles": ["normal"], "subsets": ["khmer", "latin"]}, "Tauri": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Taviraj": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Teachers": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["greek-ext", "latin", "latin-ext"]}, "Teko": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Tektur": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "vietnamese"]}, "Telex": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Tenali Ramakrishna": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Tenor Sans": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Text Me One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Texturina": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "opsz", "min": 12, "max": 72, "defaultValue": 12}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Thasadith": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "The Girl Next Door": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "The Nautigal": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Tienne": {"weights": ["400", "700", "900"], "styles": ["normal"], "subsets": ["latin"]}, "Tillana": {"weights": ["400", "500", "600", "700", "800"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Tilt Neon": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "XROT", "min": -45, "max": 45, "defaultValue": 0}, {"tag": "YROT", "min": -45, "max": 45, "defaultValue": 0}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Tilt Prism": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "XROT", "min": -45, "max": 45, "defaultValue": 0}, {"tag": "YROT", "min": -45, "max": 45, "defaultValue": 0}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Tilt Warp": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "XROT", "min": -45, "max": 45, "defaultValue": 0}, {"tag": "YROT", "min": -45, "max": 45, "defaultValue": 0}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Timmana": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "telugu"]}, "Tinos": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "hebrew", "latin", "latin-ext", "vietnamese"]}, "Tiny5": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext"]}, "Tiro Bangla": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["bengali", "latin", "latin-ext"]}, "Tiro Devanagari Hindi": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Tiro Devanagari Marathi": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Tiro Devanagari Sanskrit": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Tiro Gurmukhi": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["gur<PERSON><PERSON>i", "latin", "latin-ext"]}, "Tiro Kannada": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["kannada", "latin", "latin-ext"]}, "Tiro Tamil": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "tamil"]}, "Tiro Telugu": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "telugu"]}, "Titan One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Titillium Web": {"weights": ["200", "300", "400", "600", "700", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Tomorrow": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Tourney": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Trade Winds": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Train One": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Trirong": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "thai", "vietnamese"]}, "Trispace": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Trocchi": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Trochut": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "Truculenta": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "opsz", "min": 12, "max": 72, "defaultValue": 14}, {"tag": "wdth", "min": 75, "max": 125, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Trykker": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Tsukimi Rounded": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Tulpen One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Turret Road": {"weights": ["200", "300", "400", "500", "700", "800"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Twinkle Star": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Ubuntu": {"weights": ["300", "400", "500", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext"]}, "Ubuntu Condensed": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext"]}, "Ubuntu Mono": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext"]}, "Ubuntu Sans": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wdth", "min": 75, "max": 100, "defaultValue": 100}, {"tag": "wght", "min": 100, "max": 800, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext"]}, "Ubuntu Sans Mono": {"weights": ["400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "greek-ext", "latin", "latin-ext"]}, "Uchen": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "tibetan"]}, "Ultra": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Unbounded": {"weights": ["200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Uncial Antiqua": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Underdog": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Unica One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "UnifrakturCook": {"weights": ["700"], "styles": ["normal"], "subsets": ["latin"]}, "UnifrakturMaguntia": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Unkempt": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin"]}, "Unlock": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Unna": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Updock": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Urbanist": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "VT323": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Vampiro One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Varela": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Varela Round": {"weights": ["400"], "styles": ["normal"], "subsets": ["hebrew", "latin", "latin-ext", "vietnamese"]}, "Varta": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Vast Shadow": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Vazirmatn": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["arabic", "latin", "latin-ext"]}, "Vesper Libre": {"weights": ["400", "500", "700", "900"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Viaoda Libre": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Vibes": {"weights": ["400"], "styles": ["normal"], "subsets": ["arabic", "latin"]}, "Vibur": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Victor Mono": {"weights": ["100", "200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "vietnamese"]}, "Vidaloka": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Viga": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Vina Sans": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Voces": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Volkhov": {"weights": ["400", "700"], "styles": ["normal", "italic"], "subsets": ["latin"]}, "Vollkorn": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "vietnamese"]}, "Vollkorn SC": {"weights": ["400", "600", "700", "900"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Voltaire": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Vujahday Script": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Waiting for the Sunrise": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Wallpoet": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Walter Turncoat": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Warnes": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Water Brush": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Waterfall": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Wavefont": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal"], "axes": [{"tag": "ROND", "min": 0, "max": 100, "defaultValue": 100}, {"tag": "YELA", "min": -100, "max": 100, "defaultValue": -100}, {"tag": "wght", "min": 4, "max": 1000, "defaultValue": 400}], "subsets": []}, "Wellfleet": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Wendy One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Whisper": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "WindSong": {"weights": ["400", "500"], "styles": ["normal"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Wire One": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Wittgenstein": {"weights": ["400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext"]}, "Wix Madefor Display": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Wix Madefor Text": {"weights": ["400", "500", "600", "700", "800", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 400, "max": 800, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Work Sans": {"weights": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 100, "max": 900, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Workbench": {"weights": ["400", "variable"], "styles": ["normal"], "axes": [{"tag": "BLED", "min": 0, "max": 100, "defaultValue": 0}, {"tag": "SCAN", "min": -53, "max": 100, "defaultValue": 0}], "subsets": ["latin", "math", "symbols"]}, "Xanh Mono": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Yaldevi": {"weights": ["200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "sinhala"]}, "Yanone Kaffeesatz": {"weights": ["200", "300", "400", "500", "600", "700", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 200, "max": 700, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Yantramanav": {"weights": ["100", "300", "400", "500", "700", "900"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Yarndings 12": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "math", "symbols"]}, "Yarndings 12 Charted": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "math", "symbols"]}, "Yarndings 20": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "math", "symbols"]}, "Yarndings 20 Charted": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "math", "symbols"]}, "Yatra One": {"weights": ["400"], "styles": ["normal"], "subsets": ["<PERSON><PERSON><PERSON><PERSON>", "latin", "latin-ext"]}, "Yellowtail": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Yeon Sung": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Yeseva One": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "cyrillic-ext", "latin", "latin-ext", "vietnamese"]}, "Yesteryear": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Yomogi": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext", "vietnamese"]}, "Young Serif": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Yrsa": {"weights": ["300", "400", "500", "600", "700", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 300, "max": 700, "defaultValue": 400}], "subsets": ["latin", "latin-ext", "vietnamese"]}, "Ysabeau": {"weights": ["1", "100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 1, "max": 1000, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Ysabeau Infant": {"weights": ["1", "100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 1, "max": 1000, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Ysabeau Office": {"weights": ["1", "100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal", "italic"], "axes": [{"tag": "wght", "min": 1, "max": 1000, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Ysabeau SC": {"weights": ["1", "100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "variable"], "styles": ["normal"], "axes": [{"tag": "wght", "min": 1, "max": 1000, "defaultValue": 400}], "subsets": ["cyrillic", "cyrillic-ext", "greek", "latin", "latin-ext", "math", "symbols", "vietnamese"]}, "Yuji Boku": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Yuji Hentaigana Akari": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Yuji Hentaigana Akebono": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Yuji Mai": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Yuji Syuku": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Yusei Magic": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "ZCOOL KuaiLe": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "ZCOOL QingKe HuangYou": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "ZCOOL XiaoWei": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Zain": {"weights": ["200", "300", "400", "700", "800", "900"], "styles": ["normal", "italic"], "subsets": ["arabic", "latin"]}, "Zen Antique": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "greek", "latin", "latin-ext"]}, "Zen Antique Soft": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "greek", "latin", "latin-ext"]}, "Zen Dots": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Zen Kaku Gothic Antique": {"weights": ["300", "400", "500", "700", "900"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Zen Kaku Gothic New": {"weights": ["300", "400", "500", "700", "900"], "styles": ["normal"], "subsets": ["cyrillic", "latin", "latin-ext"]}, "Zen Kurenaido": {"weights": ["400"], "styles": ["normal"], "subsets": ["cyrillic", "greek", "latin", "latin-ext"]}, "Zen Loop": {"weights": ["400"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Zen Maru Gothic": {"weights": ["300", "400", "500", "700", "900"], "styles": ["normal"], "subsets": ["cyrillic", "greek", "latin", "latin-ext"]}, "Zen Old Mincho": {"weights": ["400", "500", "600", "700", "900"], "styles": ["normal"], "subsets": ["cyrillic", "greek", "latin", "latin-ext"]}, "Zen Tokyo Zoo": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Zeyada": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}, "Zhi Mang Xing": {"weights": ["400"], "styles": ["normal"], "subsets": ["latin"]}, "Zilla Slab": {"weights": ["300", "400", "500", "600", "700"], "styles": ["normal", "italic"], "subsets": ["latin", "latin-ext"]}, "Zilla Slab Highlight": {"weights": ["400", "700"], "styles": ["normal"], "subsets": ["latin", "latin-ext"]}}