(()=>{"use strict";if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var e={};(()=>{var r=e;Object.defineProperty(r,"__esModule",{value:true});function lexer(e){var r=[];var n=0;while(n<e.length){var t=e[n];if(t==="*"||t==="+"||t==="?"){r.push({type:"MODIFIER",index:n,value:e[n++]});continue}if(t==="\\"){r.push({type:"ESCAPED_CHAR",index:n++,value:e[n++]});continue}if(t==="{"){r.push({type:"OPEN",index:n,value:e[n++]});continue}if(t==="}"){r.push({type:"CLOSE",index:n,value:e[n++]});continue}if(t===":"){var i="";var a=n+1;while(a<e.length){var o=e.charCodeAt(a);if(o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122||o===95){i+=e[a++];continue}break}if(!i)throw new TypeError("Missing parameter name at "+n);r.push({type:"NAME",index:n,value:i});n=a;continue}if(t==="("){var f=1;var u="";var a=n+1;if(e[a]==="?"){throw new TypeError('Pattern cannot start with "?" at '+a)}while(a<e.length){if(e[a]==="\\"){u+=e[a++]+e[a++];continue}if(e[a]===")"){f--;if(f===0){a++;break}}else if(e[a]==="("){f++;if(e[a+1]!=="?"){throw new TypeError("Capturing groups are not allowed at "+a)}}u+=e[a++]}if(f)throw new TypeError("Unbalanced pattern at "+n);if(!u)throw new TypeError("Missing pattern at "+n);r.push({type:"PATTERN",index:n,value:u});n=a;continue}r.push({type:"CHAR",index:n,value:e[n++]})}r.push({type:"END",index:n,value:""});return r}function parse(e,r){if(r===void 0){r={}}var n=lexer(e);var t=r.prefixes,i=t===void 0?"./":t;var a="[^"+escapeString(r.delimiter||"/#?")+"]+?";var o=[];var f=0;var u=0;var p="";var tryConsume=function(e){if(u<n.length&&n[u].type===e)return n[u++].value};var mustConsume=function(e){var r=tryConsume(e);if(r!==undefined)return r;var t=n[u],i=t.type,a=t.index;throw new TypeError("Unexpected "+i+" at "+a+", expected "+e)};var consumeText=function(){var e="";var r;while(r=tryConsume("CHAR")||tryConsume("ESCAPED_CHAR")){e+=r}return e};while(u<n.length){var v=tryConsume("CHAR");var c=tryConsume("NAME");var s=tryConsume("PATTERN");if(c||s){var d=v||"";if(i.indexOf(d)===-1){p+=d;d=""}if(p){o.push(p);p=""}o.push({name:c||f++,prefix:d,suffix:"",pattern:s||a,modifier:tryConsume("MODIFIER")||""});continue}var g=v||tryConsume("ESCAPED_CHAR");if(g){p+=g;continue}if(p){o.push(p);p=""}var x=tryConsume("OPEN");if(x){var d=consumeText();var l=tryConsume("NAME")||"";var h=tryConsume("PATTERN")||"";var m=consumeText();mustConsume("CLOSE");o.push({name:l||(h?f++:""),pattern:l&&!h?a:h,prefix:d,suffix:m,modifier:tryConsume("MODIFIER")||""});continue}mustConsume("END")}return o}r.parse=parse;function compile(e,r){return tokensToFunction(parse(e,r),r)}r.compile=compile;function tokensToFunction(e,r){if(r===void 0){r={}}var n=flags(r);var t=r.encode,i=t===void 0?function(e){return e}:t,a=r.validate,o=a===void 0?true:a;var f=e.map((function(e){if(typeof e==="object"){return new RegExp("^(?:"+e.pattern+")$",n)}}));return function(r){var n="";for(var t=0;t<e.length;t++){var a=e[t];if(typeof a==="string"){n+=a;continue}var u=r?r[a.name]:undefined;var p=a.modifier==="?"||a.modifier==="*";var v=a.modifier==="*"||a.modifier==="+";if(Array.isArray(u)){if(!v){throw new TypeError('Expected "'+a.name+'" to not repeat, but got an array')}if(u.length===0){if(p)continue;throw new TypeError('Expected "'+a.name+'" to not be empty')}for(var c=0;c<u.length;c++){var s=i(u[c],a);if(o&&!f[t].test(s)){throw new TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+s+'"')}n+=a.prefix+s+a.suffix}continue}if(typeof u==="string"||typeof u==="number"){var s=i(String(u),a);if(o&&!f[t].test(s)){throw new TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+s+'"')}n+=a.prefix+s+a.suffix;continue}if(p)continue;var d=v?"an array":"a string";throw new TypeError('Expected "'+a.name+'" to be '+d)}return n}}r.tokensToFunction=tokensToFunction;function match(e,r){var n=[];var t=pathToRegexp(e,n,r);return regexpToFunction(t,n,r)}r.match=match;function regexpToFunction(e,r,n){if(n===void 0){n={}}var t=n.decode,i=t===void 0?function(e){return e}:t;return function(n){var t=e.exec(n);if(!t)return false;var a=t[0],o=t.index;var f=Object.create(null);var _loop_1=function(e){if(t[e]===undefined)return"continue";var n=r[e-1];if(n.modifier==="*"||n.modifier==="+"){f[n.name]=t[e].split(n.prefix+n.suffix).map((function(e){return i(e,n)}))}else{f[n.name]=i(t[e],n)}};for(var u=1;u<t.length;u++){_loop_1(u)}return{path:a,index:o,params:f}}}r.regexpToFunction=regexpToFunction;function escapeString(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function flags(e){return e&&e.sensitive?"":"i"}function regexpToRegexp(e,r){if(!r)return e;var n=e.source.match(/\((?!\?)/g);if(n){for(var t=0;t<n.length;t++){r.push({name:t,prefix:"",suffix:"",modifier:"",pattern:""})}}return e}function arrayToRegexp(e,r,n){var t=e.map((function(e){return pathToRegexp(e,r,n).source}));return new RegExp("(?:"+t.join("|")+")",flags(n))}function stringToRegexp(e,r,n){return tokensToRegexp(parse(e,n),r,n)}function tokensToRegexp(e,r,n){if(n===void 0){n={}}var t=n.strict,i=t===void 0?false:t,a=n.start,o=a===void 0?true:a,f=n.end,u=f===void 0?true:f,p=n.encode,v=p===void 0?function(e){return e}:p;var c="["+escapeString(n.endsWith||"")+"]|$";var s="["+escapeString(n.delimiter||"/#?")+"]";var d=o?"^":"";for(var g=0,x=e;g<x.length;g++){var l=x[g];if(typeof l==="string"){d+=escapeString(v(l))}else{var h=escapeString(v(l.prefix));var m=escapeString(v(l.suffix));if(l.pattern){if(r)r.push(l);if(h||m){if(l.modifier==="+"||l.modifier==="*"){var E=l.modifier==="*"?"?":"";d+="(?:"+h+"((?:"+l.pattern+")(?:"+m+h+"(?:"+l.pattern+"))*)"+m+")"+E}else{d+="(?:"+h+"("+l.pattern+")"+m+")"+l.modifier}}else{d+="("+l.pattern+")"+l.modifier}}else{d+="(?:"+h+m+")"+l.modifier}}}if(u){if(!i)d+=s+"?";d+=!n.endsWith?"$":"(?="+c+")"}else{var T=e[e.length-1];var y=typeof T==="string"?s.indexOf(T[T.length-1])>-1:T===undefined;if(!i){d+="(?:"+s+"(?="+c+"))?"}if(!y){d+="(?="+s+"|"+c+")"}}return new RegExp(d,flags(n))}r.tokensToRegexp=tokensToRegexp;function pathToRegexp(e,r,n){if(e instanceof RegExp)return regexpToRegexp(e,r);if(Array.isArray(e))return arrayToRegexp(e,r,n);return stringToRegexp(e,r,n)}r.pathToRegexp=pathToRegexp})();module.exports=e})();