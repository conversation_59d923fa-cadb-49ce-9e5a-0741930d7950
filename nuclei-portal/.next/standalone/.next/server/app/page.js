(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},554:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/root/nuclei/nuclei-portal/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/root/nuclei/nuclei-portal/src/app/page.tsx","default")},1437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return s},isInterceptionRouteAppPath:function(){return a}});let n=r(4722),i=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function s(e){let t,r,a;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,a]=e.split(r,2);break}if(!t||!r||!a)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":a="/"===t?"/"+a:t+"/"+a;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let s=t.split("/");if(s.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});a=s.slice(0,-2).concat(a).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:a}}},1658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return p},normalizeMetadataPageToRoute:function(){return m},normalizeMetadataRoute:function(){return f}});let n=r(8304),i=function(e){return e&&e.__esModule?e:{default:e}}(r(8671)),a=r(6341),s=r(4396),o=r(660),l=r(4722),c=r(2958),u=r(5499);function d(e){let t=i.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,u.isGroupSegment)(e)||(0,u.isParallelRouteSegment)(e))&&(r=(0,o.djb2Hash)(t).toString(36).slice(0,6)),r}function p(e,t,r){let n=(0,l.normalizeAppPath)(e),o=(0,s.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),u=(0,a.interpolateDynamicPath)(n,t,o),{name:p,ext:f}=i.default.parse(r),m=d(i.default.posix.join(e,p)),h=m?`-${m}`:"";return(0,c.normalizePathSep)(i.default.join(u,`${p}${h}${f}`))}function f(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=d(e),!t.endsWith("/route")){let{dir:e,name:n,ext:a}=i.default.parse(t);t=i.default.posix.join(e,`${n}${r?`-${r}`:""}${a}`,"route")}return t}function m(e,t){let r=e.endsWith("/route"),n=r?e.slice(0,-6):e,i=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${i}`)+(r?"/route":"")}},2437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=r(5362);function i(e,t){let r=[],i=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,r);return(e,n)=>{if("string"!=typeof e)return!1;let i=a(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},2785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},2958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3675:()=>{},3736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),r(4827);let n=r(2785);function i(e,t,r){void 0===r&&(r=!0);let i=new URL("http://n"),a=t?new URL(t,i):e.startsWith(".")?new URL("http://n"):i,{pathname:s,searchParams:o,search:l,hash:c,href:u,origin:d}=new URL(e,a);if(d!==i.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:s,query:r?(0,n.searchParamsToUrlQuery)(o):void 0,search:l,hash:c,href:u.slice(d.length)}}},3873:e=>{"use strict";e.exports=require("path")},4396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return h},getNamedRouteRegex:function(){return m},getRouteRegex:function(){return d},parseParameter:function(){return l}});let n=r(6143),i=r(1437),a=r(3293),s=r(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?c(t[2]):c(e)}function c(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function u(e,t,r){let n={},l=1,u=[];for(let d of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),s=d.match(o);if(e&&s&&s[2]){let{key:t,optional:r,repeat:i}=c(s[2]);n[t]={pos:l++,repeat:i,optional:r},u.push("/"+(0,a.escapeStringRegexp)(e)+"([^/]+?)")}else if(s&&s[2]){let{key:e,repeat:t,optional:i}=c(s[2]);n[e]={pos:l++,repeat:t,optional:i},r&&s[1]&&u.push("/"+(0,a.escapeStringRegexp)(s[1]));let o=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&s[1]&&(o=o.substring(1)),u.push(o)}else u.push("/"+(0,a.escapeStringRegexp)(d));t&&s&&s[3]&&u.push((0,a.escapeStringRegexp)(s[3]))}return{parameterizedRoute:u.join(""),groups:n}}function d(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:a,groups:s}=u(e,r,n),o=a;return i||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:s}}function p(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:i,routeKeys:s,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:u,optional:d,repeat:p}=c(i),f=u.replace(/\W/g,"");o&&(f=""+o+f);let m=!1;(0===f.length||f.length>30)&&(m=!0),isNaN(parseInt(f.slice(0,1)))||(m=!0),m&&(f=n());let h=f in s;o?s[f]=""+o+u:s[f]=u;let g=r?(0,a.escapeStringRegexp)(r):"";return t=h&&l?"\\k<"+f+">":p?"(?<"+f+">.+?)":"(?<"+f+">[^/]+?)",d?"(?:/"+g+t+")?":"/"+g+t}function f(e,t,r,l,c){let u,d=(u=0,()=>{let e="",t=++u;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),f={},m=[];for(let u of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>u.startsWith(e)),s=u.match(o);if(e&&s&&s[2])m.push(p({getSafeRouteKey:d,interceptionMarker:s[1],segment:s[2],routeKeys:f,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:c}));else if(s&&s[2]){l&&s[1]&&m.push("/"+(0,a.escapeStringRegexp)(s[1]));let e=p({getSafeRouteKey:d,segment:s[2],routeKeys:f,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:c});l&&s[1]&&(e=e.substring(1)),m.push(e)}else m.push("/"+(0,a.escapeStringRegexp)(u));r&&s&&s[3]&&m.push((0,a.escapeStringRegexp)(s[3]))}return{namedParameterizedRoute:m.join(""),routeKeys:f}}function m(e,t){var r,n,i;let a=f(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(i=t.backreferenceDuplicateKeys)&&i),s=a.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(s+="(?:/)?"),{...d(e,t),namedRegex:"^"+s+"$",routeKeys:a.routeKeys}}function h(e,t){let{parameterizedRoute:r}=u(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=f(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l});var n=r(7413),i=r(2202),a=r.n(i),s=r(4988),o=r.n(s);r(1135);let l={title:"Create Next App",description:"Generated by create next app"};function c({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${a().variable} ${o().variable} antialiased`,children:e})})}},4722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return s}});let n=r(5531),i=r(5499);function a(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function s(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return x},NormalizeError:function(){return h},PageNotFoundError:function(){return g},SP:function(){return p},ST:function(){return f},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return o},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function s(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function o(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let p="undefined"!=typeof performance,f=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class h extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class x extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},5256:(e,t,r)=>{Promise.resolve().then(r.bind(r,1204))},5272:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",a=r+1;a<e.length;){var s=e.charCodeAt(a);if(s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||95===s){i+=e[a++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=a;continue}if("("===n){var o=1,l="",a=r+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--o){a++;break}}else if("("===e[a]&&(o++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);l+=e[a++]}if(o)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=a;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,a=void 0===n?"./":n,s="[^"+i(t.delimiter||"/#?")+"]+?",o=[],l=0,c=0,u="",d=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},p=function(e){var t=d(e);if(void 0!==t)return t;var n=r[c];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},f=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};c<r.length;){var m=d("CHAR"),h=d("NAME"),g=d("PATTERN");if(h||g){var x=m||"";-1===a.indexOf(x)&&(u+=x,x=""),u&&(o.push(u),u=""),o.push({name:h||l++,prefix:x,suffix:"",pattern:g||s,modifier:d("MODIFIER")||""});continue}var y=m||d("ESCAPED_CHAR");if(y){u+=y;continue}if(u&&(o.push(u),u=""),d("OPEN")){var x=f(),b=d("NAME")||"",v=d("PATTERN")||"",j=f();p("CLOSE"),o.push({name:b||(v?l++:""),pattern:b&&!v?s:v,prefix:x,suffix:j,modifier:d("MODIFIER")||""});continue}p("END")}return o}function r(e,t){void 0===t&&(t={});var r=a(t),n=t.encode,i=void 0===n?function(e){return e}:n,s=t.validate,o=void 0===s||s,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var a=e[n];if("string"==typeof a){r+=a;continue}var s=t?t[a.name]:void 0,c="?"===a.modifier||"*"===a.modifier,u="*"===a.modifier||"+"===a.modifier;if(Array.isArray(s)){if(!u)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===s.length){if(c)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var d=0;d<s.length;d++){var p=i(s[d],a);if(o&&!l[n].test(p))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+p+'"');r+=a.prefix+p+a.suffix}continue}if("string"==typeof s||"number"==typeof s){var p=i(String(s),a);if(o&&!l[n].test(p))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+p+'"');r+=a.prefix+p+a.suffix;continue}if(!c){var f=u?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+f)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,i=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var a=n[0],s=n.index,o=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?o[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):o[r.name]=i(n[e],r)}}(l);return{path:a,index:s,params:o}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function s(e,t,r){void 0===r&&(r={});for(var n=r.strict,s=void 0!==n&&n,o=r.start,l=r.end,c=r.encode,u=void 0===c?function(e){return e}:c,d="["+i(r.endsWith||"")+"]|$",p="["+i(r.delimiter||"/#?")+"]",f=void 0===o||o?"^":"",m=0;m<e.length;m++){var h=e[m];if("string"==typeof h)f+=i(u(h));else{var g=i(u(h.prefix)),x=i(u(h.suffix));if(h.pattern)if(t&&t.push(h),g||x)if("+"===h.modifier||"*"===h.modifier){var y="*"===h.modifier?"?":"";f+="(?:"+g+"((?:"+h.pattern+")(?:"+x+g+"(?:"+h.pattern+"))*)"+x+")"+y}else f+="(?:"+g+"("+h.pattern+")"+x+")"+h.modifier;else f+="("+h.pattern+")"+h.modifier;else f+="(?:"+g+x+")"+h.modifier}}if(void 0===l||l)s||(f+=p+"?"),f+=r.endsWith?"(?="+d+")":"$";else{var b=e[e.length-1],v="string"==typeof b?p.indexOf(b[b.length-1])>-1:void 0===b;s||(f+="(?:"+p+"(?="+d+"))?"),v||(f+="(?="+p+"|"+d+")")}return new RegExp(f,a(r))}function o(t,r,n){if(t instanceof RegExp){if(!r)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var l=0;l<i.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,r,n).source}).join("|")+")",a(n)):s(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(o(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=s,t.pathToRegexp=o})(),e.exports=t})()},5526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return u},matchHas:function(){return c},parseDestination:function(){return d},prepareDestination:function(){return p}});let n=r(5362),i=r(3293),a=r(6759),s=r(1437),o=r(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let i={},a=r=>{let n,a=r.key;switch(r.type){case"header":a=a.toLowerCase(),n=e.headers[a];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,o.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return i[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(a)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===r.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!r.every(e=>a(e))||n.some(e=>a(e)))&&i}function u(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,a.parseUrl)(t),n=r.pathname;n&&(n=l(n));let s=r.href;s&&(s=l(s));let o=r.hostname;o&&(o=l(o));let c=r.hash;return c&&(c=l(c)),{...r,pathname:n,hostname:o,href:s,hash:c}}function p(e){let t,r,i=Object.assign({},e.query),a=d(e),{hostname:o,query:c}=a,p=a.pathname;a.hash&&(p=""+p+a.hash);let f=[],m=[];for(let e of((0,n.pathToRegexp)(p,m),m))f.push(e.name);if(o){let e=[];for(let t of((0,n.pathToRegexp)(o,e),e))f.push(t.name)}let h=(0,n.compile)(p,{validate:!1});for(let[r,i]of(o&&(t=(0,n.compile)(o,{validate:!1})),Object.entries(c)))Array.isArray(i)?c[r]=i.map(t=>u(l(t),e.params)):"string"==typeof i&&(c[r]=u(l(i),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>f.includes(e)))for(let t of g)t in c||(c[t]=e.params[t]);if((0,s.isInterceptionRouteAppPath)(p))for(let t of p.split("/")){let r=s.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,i]=(r=h(e.params)).split("#",2);t&&(a.hostname=t(e.params)),a.pathname=n,a.hash=(i?"#":"")+(i||""),delete a.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return a.query={...i,...a.query},{newUrl:r,destQuery:c,parsedDestination:a}}},5531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},5544:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return x},getUtils:function(){return g},interpolateDynamicPath:function(){return m},normalizeDynamicRouteParams:function(){return h},normalizeVercelUrl:function(){return f}});let n=r(9551),i=r(1959),a=r(2437),s=r(4396),o=r(8034),l=r(5526),c=r(2887),u=r(4722),d=r(6143),p=r(7912);function f(e,t,r){let i=(0,n.parse)(e.url,!0);for(let e of(delete i.search,Object.keys(i.query))){let n=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),a=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(n||a||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete i.query[e]}e.url=(0,n.format)(i)}function m(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let i,{optional:a,repeat:s}=r.groups[n],o=`[${s?"...":""}${n}]`;a&&(o=`[${o}]`);let l=t[n];i=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,i)}return e}function h(e,t,r,n){let i={};for(let a of Object.keys(t.groups)){let s=e[a];"string"==typeof s?s=(0,u.normalizeRscURL)(s):Array.isArray(s)&&(s=s.map(u.normalizeRscURL));let o=r[a],l=t.groups[a].optional;if((Array.isArray(o)?o.some(e=>Array.isArray(s)?s.some(t=>t.includes(e)):null==s?void 0:s.includes(e)):null==s?void 0:s.includes(o))||void 0===s&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!s||Array.isArray(s)&&1===s.length&&("index"===s[0]||s[0]===`[[...${a}]]`))&&(s=void 0,delete e[a]),s&&"string"==typeof s&&t.groups[a].repeat&&(s=s.split("/")),s&&(i[a]=s)}return{params:i,hasValidParams:!0}}function g({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:u,trailingSlash:d,caseSensitive:g}){let x,y,b;return u&&(x=(0,s.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),b=(y=(0,o.getRouteMatcher)(x))(e)),{handleRewrites:function(s,o){let p={},f=o.pathname,m=n=>{let c=(0,a.getPathMatch)(n.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!o.pathname)return!1;let m=c(o.pathname);if((n.has||n.missing)&&m){let e=(0,l.matchHas)(s,o.query,n.has,n.missing);e?Object.assign(m,e):m=!1}if(m){let{parsedDestination:a,destQuery:s}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:m,query:o.query});if(a.protocol)return!0;if(Object.assign(p,s,m),Object.assign(o.query,a.query),delete a.query,Object.assign(o,a),!(f=o.pathname))return!1;if(r&&(f=f.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(f,t.locales);f=e.pathname,o.query.nextInternalLocale=e.detectedLocale||m.nextInternalLocale}if(f===e)return!0;if(u&&y){let e=y(f);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])m(e);if(f!==e){let t=!1;for(let e of n.afterFiles||[])if(t=m(e))break;if(!t&&!(()=>{let t=(0,c.removeTrailingSlash)(f||"");return t===(0,c.removeTrailingSlash)(e)||(null==y?void 0:y(t))})()){for(let e of n.fallback||[])if(t=m(e))break}}return p},defaultRouteRegex:x,dynamicRouteMatcher:y,defaultRouteMatches:b,getParamsFromRouteMatches:function(e){if(!x)return null;let{groups:t,routeKeys:r}=x,n=(0,o.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,p.normalizeNextQueryParam)(e);r&&(n[r]=t,delete n[e])}let i={};for(let e of Object.keys(r)){let a=r[e];if(!a)continue;let s=t[a],o=n[e];if(!s.optional&&!o)return null;i[s.pos]=o}return i}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>x&&b?h(e,x,b,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>f(e,t,x),interpolateDynamicPath:(e,t)=>m(e,t,x)}}function x(e,t){return"string"==typeof e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[d.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(n),s=(r||{}).decode||e,o=0;o<a.length;o++){var l=a[o],c=l.indexOf("=");if(!(c<0)){var u=l.substr(0,c).trim(),d=l.substr(++c,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[u]&&(i[u]=function(e,t){try{return t(e)}catch(t){return e}}(d,s))}}return i},t.serialize=function(e,t,n){var a=n||{},s=a.encode||r;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var o=s(t);if(o&&!i.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=a.maxAge){var c=a.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let n=r(2785),i=r(3736);function a(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},6819:()=>{},6919:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>R});var n=r(687),i=r(3210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:a="",children:s,iconNode:o,...d},p)=>(0,i.createElement)("svg",{ref:p,...u,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:l("lucide",a),...!s&&!c(d)&&{"aria-hidden":"true"},...d},[...o.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(s)?s:[s]])),p=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...n},s)=>(0,i.createElement)(d,{ref:s,iconNode:t,className:l(`lucide-${a(o(e))}`,`lucide-${e}`,r),...n}));return r.displayName=o(e),r},f=p("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),m=p("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),h=p("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),g=p("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),x=p("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),y=p("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),b=p("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),v=p("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),j=p("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),E=p("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);function R(){let[e,t]=(0,i.useState)(!1),[r,a]=(0,i.useState)(null),[s,o]=(0,i.useState)("dashboard");return e?(0,n.jsx)(N,{user:r,currentView:s,setCurrentView:o}):(0,n.jsx)(_,{onLogin:t,setUser:a})}function _({onLogin:e,setUser:t}){let[r,a]=(0,i.useState)(!0),[s,o]=(0,i.useState)(""),[l,c]=(0,i.useState)(""),[u,d]=(0,i.useState)(!1),[p,m]=(0,i.useState)(""),h=async n=>{n.preventDefault(),d(!0),m("");try{let n=await fetch(r?"/api/auth/login":"/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:s,password:l})}),i=await n.json();n.ok?(localStorage.setItem("token",i.token),t(i.user),e(!0)):m(i.error||"Authentication failed")}catch{m("Network error. Please try again.")}finally{d(!1)}};return(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,n.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-xl p-8",children:[(0,n.jsxs)("div",{className:"text-center mb-8",children:[(0,n.jsx)(f,{className:"mx-auto h-12 w-12 text-indigo-600 mb-4"}),(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Nuclei Portal"}),(0,n.jsx)("p",{className:"text-gray-600 mt-2",children:"Professional Vulnerability Scanner"})]}),(0,n.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,n.jsx)("input",{type:"email",value:s,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,n.jsx)("input",{type:"password",value:l,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500",required:!0})]}),p&&(0,n.jsx)("div",{className:"text-red-600 text-sm text-center",children:p}),(0,n.jsx)("button",{type:"submit",disabled:u,className:"w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50",children:u?"Processing...":r?"Sign In":"Sign Up"})]}),(0,n.jsx)("div",{className:"mt-6 text-center",children:(0,n.jsx)("button",{onClick:()=>a(!r),className:"text-indigo-600 hover:text-indigo-500 text-sm",children:r?"Don't have an account? Sign up":"Already have an account? Sign in"})}),(0,n.jsx)("div",{className:"mt-6 p-4 bg-gray-50 rounded-md",children:(0,n.jsxs)("p",{className:"text-sm text-gray-600 text-center",children:["Demo credentials:",(0,n.jsx)("br",{}),(0,n.jsx)("strong",{children:"<EMAIL>"}),(0,n.jsx)("br",{}),(0,n.jsx)("strong",{children:"admin123"})]})})]})})}function N({user:e,currentView:t,setCurrentView:r}){return(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,n.jsx)("nav",{className:"bg-white shadow-sm border-b",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"flex justify-between h-16",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(f,{className:"h-8 w-8 text-indigo-600 mr-3"}),(0,n.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Nuclei Portal"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("span",{className:"text-sm text-gray-600",children:"Welcome back!"}),(0,n.jsx)("button",{onClick:()=>{localStorage.removeItem("token"),window.location.reload()},className:"text-sm text-indigo-600 hover:text-indigo-500",children:"Sign Out"})]})]})})}),(0,n.jsx)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:["dashboard"===t&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,n.jsx)(P,{title:"New Scan",description:"Start a vulnerability scan",icon:m,color:"bg-blue-500",onClick:()=>r("new-scan")}),(0,n.jsx)(P,{title:"Reports",description:"View scan reports",icon:h,color:"bg-green-500",onClick:()=>r("reports")}),(0,n.jsx)(P,{title:"Analytics",description:"Scan statistics",icon:g,color:"bg-purple-500",onClick:()=>r("analytics")}),(0,n.jsx)(P,{title:"Settings",description:"Configure portal",icon:x,color:"bg-gray-500",onClick:()=>r("settings")})]}),(0,n.jsx)(w,{user:e})]}),"new-scan"===t&&(0,n.jsx)(A,{user:e,onBack:()=>r("dashboard")}),"reports"===t&&(0,n.jsx)(S,{user:e,onBack:()=>r("dashboard")}),"analytics"===t&&(0,n.jsx)(O,{user:e,onBack:()=>r("dashboard")}),"settings"===t&&(0,n.jsx)(k,{user:e,onBack:()=>r("dashboard")})]})})]})}function P({title:e,description:t,icon:r,color:i,onClick:a}){return(0,n.jsx)("div",{className:"bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow cursor-pointer",onClick:a,children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:`${i} p-3 rounded-lg`,children:(0,n.jsx)(r,{className:"h-6 w-6 text-white"})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:t})]})]})})}function w({user:e}){let[t,r]=(0,i.useState)([]),[a,s]=(0,i.useState)(!0),o=e=>{switch(e){case"completed":return(0,n.jsx)(y,{className:"h-5 w-5 text-green-500"});case"running":return(0,n.jsx)(b,{className:"h-5 w-5 text-blue-500"});case"failed":return(0,n.jsx)(v,{className:"h-5 w-5 text-red-500"});default:return(0,n.jsx)(b,{className:"h-5 w-5 text-gray-500"})}};return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent Scans"}),a?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"}),(0,n.jsx)("p",{className:"mt-2 text-gray-500",children:"Loading scans..."})]}):0===t.length?(0,n.jsxs)("div",{className:"text-center py-12 text-gray-500",children:[(0,n.jsx)(m,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,n.jsx)("p",{children:"No scans yet. Start your first vulnerability scan!"})]}):(0,n.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,n.jsx)("div",{className:"border rounded-lg p-4 hover:bg-gray-50",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[o(e.status),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-medium text-gray-900",children:e.target}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:[new Date(e.created_at).toLocaleDateString()," - ",e.templates]})]})]}),(0,n.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${"completed"===e.status?"bg-green-100 text-green-800":"running"===e.status?"bg-blue-100 text-blue-800":"failed"===e.status?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:e.status})]})},e.id))})]})}function A({user:e,onBack:t}){let[r,a]=(0,i.useState)(""),[s,o]=(0,i.useState)("basic"),[l,c]=(0,i.useState)(!1),[u,d]=(0,i.useState)(""),[p,m]=(0,i.useState)(""),h=async e=>{e.preventDefault(),c(!0),d(""),m("");try{let e=localStorage.getItem("token"),n=await fetch("/api/scans",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},body:JSON.stringify({target:r,templates:s})}),i=await n.json();n.ok?(m(`Scan started successfully! Scan ID: ${i.scan.id}`),a(""),setTimeout(()=>{t()},2e3)):d(i.error||"Failed to start scan")}catch(e){d("Network error. Please try again.")}finally{c(!1)}};return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsxs)("div",{className:"flex items-center mb-6",children:[(0,n.jsx)("button",{onClick:t,className:"mr-4 p-2 hover:bg-gray-100 rounded-lg",children:(0,n.jsx)(j,{className:"h-5 w-5"})}),(0,n.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"New Vulnerability Scan"})]}),(0,n.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Target URL or IP"}),(0,n.jsx)("input",{type:"text",value:r,onChange:e=>a(e.target.value),placeholder:"https://example.com or ***********",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500",required:!0}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Enter a URL, IP address, or domain name to scan"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Template Selection"}),(0,n.jsxs)("select",{value:s,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500",children:[(0,n.jsx)("option",{value:"basic",children:"Basic Templates (CVEs + Vulnerabilities)"}),(0,n.jsx)("option",{value:"all",children:"All Templates (Comprehensive Scan)"})]}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"basic"===s?"Fast scan with essential vulnerability checks":"Complete scan with all available templates (slower)"})]}),(0,n.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4",children:(0,n.jsxs)("div",{className:"flex",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)(f,{className:"h-5 w-5 text-blue-400"})}),(0,n.jsxs)("div",{className:"ml-3",children:[(0,n.jsxs)("h3",{className:"text-sm font-medium text-blue-800",children:["Your Plan: ",e?.plan||"Free"]}),(0,n.jsx)("div",{className:"mt-2 text-sm text-blue-700",children:(0,n.jsxs)("p",{children:["Scans used: ",e?.scans_used||0," / ",e?.scans_limit||10]})})]})]})}),u&&(0,n.jsx)("div",{className:"text-red-600 text-sm bg-red-50 border border-red-200 rounded-md p-3",children:u}),p&&(0,n.jsx)("div",{className:"text-green-600 text-sm bg-green-50 border border-green-200 rounded-md p-3",children:p}),(0,n.jsxs)("div",{className:"flex space-x-4",children:[(0,n.jsx)("button",{type:"submit",disabled:l,className:"flex-1 bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50 flex items-center justify-center",children:l?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Starting Scan..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(E,{className:"h-4 w-4 mr-2"}),"Start Scan"]})}),(0,n.jsx)("button",{type:"button",onClick:t,className:"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500",children:"Cancel"})]})]})]})}function S({user:e,onBack:t}){return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsxs)("div",{className:"flex items-center mb-6",children:[(0,n.jsx)("button",{onClick:t,className:"mr-4 p-2 hover:bg-gray-100 rounded-lg",children:(0,n.jsx)(j,{className:"h-5 w-5"})}),(0,n.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Scan Reports"})]}),(0,n.jsxs)("div",{className:"text-center py-12 text-gray-500",children:[(0,n.jsx)(h,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,n.jsx)("p",{children:"Reports feature coming soon!"}),(0,n.jsx)("p",{className:"text-sm mt-2",children:"Generate and download professional HTML reports"})]})]})}function O({user:e,onBack:t}){return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsxs)("div",{className:"flex items-center mb-6",children:[(0,n.jsx)("button",{onClick:t,className:"mr-4 p-2 hover:bg-gray-100 rounded-lg",children:(0,n.jsx)(j,{className:"h-5 w-5"})}),(0,n.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Analytics"})]}),(0,n.jsxs)("div",{className:"text-center py-12 text-gray-500",children:[(0,n.jsx)(g,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,n.jsx)("p",{children:"Analytics dashboard coming soon!"}),(0,n.jsx)("p",{className:"text-sm mt-2",children:"View scan statistics and vulnerability trends"})]})]})}function k({user:e,onBack:t}){return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsxs)("div",{className:"flex items-center mb-6",children:[(0,n.jsx)("button",{onClick:t,className:"mr-4 p-2 hover:bg-gray-100 rounded-lg",children:(0,n.jsx)(j,{className:"h-5 w-5"})}),(0,n.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Settings"})]}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-md font-medium text-gray-900 mb-4",children:"Account Information"}),(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Email:"})," ",e?.email]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Plan:"})," ",e?.plan]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Scans Used:"})," ",e?.scans_used," / ",e?.scans_limit]})]})]}),(0,n.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,n.jsx)(x,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,n.jsx)("p",{children:"Advanced settings coming soon!"}),(0,n.jsx)("p",{className:"text-sm mt-2",children:"Configure API keys, notifications, and more"})]})]})]})}},7298:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c});var n=r(5239),i=r(8088),a=r(8170),s=r.n(a),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"/root/nuclei/nuclei-portal/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/root/nuclei/nuclei-portal/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["/root/nuclei/nuclei-portal/src/app/page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(4827);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},s={};for(let[e,t]of Object.entries(r)){let r=i[t.pos];void 0!==r&&(t.repeat?s[e]=r.split("/").map(e=>a(e)):s[e]=a(r))}return s}}},8212:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(6415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return s},getExtensionRegexString:function(){return l},isMetadataPage:function(){return d},isMetadataRoute:function(){return p},isMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return u}});let n=r(2958),i=r(4722),a=r(554),s={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function c(e,t,r){let i=(r?"":"?")+"$",a=`\\d?${r?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${i}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${i}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${i}`),RegExp(`[\\\\/]${s.icon.filename}${a}${l(s.icon.extensions,t)}${i}`),RegExp(`[\\\\/]${s.apple.filename}${a}${l(s.apple.extensions,t)}${i}`),RegExp(`[\\\\/]${s.openGraph.filename}${a}${l(s.openGraph.extensions,t)}${i}`),RegExp(`[\\\\/]${s.twitter.filename}${a}${l(s.twitter.extensions,t)}${i}`)],c=(0,n.normalizePathSep)(e);return o.some(e=>e.test(c))}function u(e){let t=e.replace(/\/route$/,"");return(0,a.isAppRouteRoute)(e)&&c(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function d(e){return!(0,a.isAppRouteRoute)(e)&&c(e,[],!1)}function p(e){let t=(0,i.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,a.isAppRouteRoute)(e)&&c(t,[],!1)}},8808:(e,t,r)=>{Promise.resolve().then(r.bind(r,6919))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,169],()=>r(7298));module.exports=n})();