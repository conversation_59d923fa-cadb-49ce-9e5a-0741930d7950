(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[28],{2576:(e,s,t)=>{Promise.resolve().then(t.bind(t,3054))},3054:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var a=t(5155),n=t(2115),r=t(5695),c=t(4298),l=t(9397),i=t(646),d=t(4861),o=t(4186),x=t(1684),m=t(5525),h=t(6785),u=t(1539),g=t(1243);function j(e){let{scanId:s,onComplete:t,onError:r}=e,{progress:j,connected:p}=function(e){let[s,t]=(0,n.useState)(null),[a,r]=(0,n.useState)(!1),l=(0,n.useRef)(null);return(0,n.useEffect)(()=>{if(!e)return;let s=(0,c.io)({path:"/api/socket",transports:["websocket","polling"]});return l.current=s,s.on("connect",()=>{console.log("WebSocket connected"),r(!0),s.emit("join-scan",e)}),s.on("disconnect",()=>{console.log("WebSocket disconnected"),r(!1)}),s.on("scan-progress",e=>{console.log("Received scan progress:",e),t(e)}),s.on("connect_error",e=>{console.error("WebSocket connection error:",e),r(!1)}),()=>{s&&(s.emit("leave-scan",e),s.disconnect())}},[e]),{progress:s,connected:a,joinScan:e=>{l.current&&a&&l.current.emit("join-scan",e)},leaveScan:e=>{l.current&&a&&l.current.emit("leave-scan",e)}}}(s),[N,f]=(0,n.useState)(!1);if((0,n.useEffect)(()=>{(null==j?void 0:j.status)==="completed"&&t?t():(null==j?void 0:j.status)==="failed"&&r&&r(j.error||"Scan failed")},[null==j?void 0:j.status,t,r]),!j)return(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Initializing scan..."})]})});let b=e=>{let s=Math.floor(e/60);return"".concat(s,":").concat((e%60).toString().padStart(2,"0"))};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(j.status){case"running":return(0,a.jsx)(l.A,{className:"h-5 w-5 text-blue-500 animate-pulse"});case"completed":return(0,a.jsx)(i.A,{className:"h-5 w-5 text-green-500"});case"failed":return(0,a.jsx)(d.A,{className:"h-5 w-5 text-red-500"});default:return(0,a.jsx)(o.A,{className:"h-5 w-5 text-gray-500"})}})(),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["Scan Progress - ID #",s]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[p?"Connected":"Disconnected"," • ",j.status]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[void 0!==j.timeElapsed&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:b(j.timeElapsed)})]}),(0,a.jsxs)("button",{onClick:()=>f(!N),className:"flex items-center space-x-2 px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors",children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[N?"Hide":"Show"," Logs"]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Progress"}),(0,a.jsxs)("span",{className:"font-medium",children:[j.progress,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,a.jsx)("div",{className:"h-3 rounded-full transition-all duration-300 ".concat((()=>{switch(j.status){case"running":return"bg-blue-500";case"completed":return"bg-green-500";case"failed":return"bg-red-500";default:return"bg-gray-500"}})()),style:{width:"".concat(j.progress,"%")}})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(m.A,{className:"h-8 w-8 text-red-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:j.vulnerabilitiesFound}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Vulnerabilities Found"})]})]})}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 text-blue-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:j.currentTarget||"N/A"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Current Target"})]})]})}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(u.A,{className:"h-8 w-8 text-yellow-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:j.currentTemplate||"Initializing..."}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Current Template"})]})]})})]}),"failed"===j.status&&j.error&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-red-500 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-red-800",children:"Scan Failed"}),(0,a.jsx)("p",{className:"text-sm text-red-700 mt-1",children:j.error})]})]})}),"completed"===j.status&&(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(i.A,{className:"h-5 w-5 text-green-500 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-green-800",children:"Scan Completed"}),(0,a.jsxs)("p",{className:"text-sm text-green-700 mt-1",children:["Found ",j.vulnerabilitiesFound," vulnerabilities in ",b(j.timeElapsed||0)]})]})]})}),N&&j.logs&&(0,a.jsxs)("div",{className:"bg-gray-900 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-green-400"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-400",children:"Live Logs"})]}),(0,a.jsx)("div",{className:"max-h-64 overflow-y-auto space-y-1",children:j.logs.slice(-20).map((e,s)=>(0,a.jsx)("div",{className:"text-xs font-mono text-gray-300",children:e},s))})]})]})}var p=t(7550),N=t(2657),f=t(1788);function b(){let e=(0,r.useParams)(),s=(0,r.useRouter)(),t=parseInt(e.id),[c,l]=(0,n.useState)(null),[i,d]=(0,n.useState)(!0),[o,x]=(0,n.useState)(null);(0,n.useEffect)(()=>{m()},[t]);let m=async()=>{try{let e=localStorage.getItem("token");if(!e)return void s.push("/");let a=await fetch("/api/scans/".concat(t),{headers:{Authorization:"Bearer ".concat(e)}});if(!a.ok)throw Error("Failed to fetch scan details");let n=await a.json();l(n.scan)}catch(e){x(e instanceof Error?e.message:"Unknown error")}finally{d(!1)}};return i?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"})}):o?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6 max-w-md w-full",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:o}),(0,a.jsx)("button",{onClick:()=>s.push("/dashboard"),className:"bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700",children:"Back to Dashboard"})]})})}):c?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)("button",{onClick:()=>s.push("/dashboard"),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors",children:[(0,a.jsx)(p.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Back to Dashboard"})]})}),(0,a.jsx)("div",{className:"flex items-center space-x-3",children:"completed"===c.status&&c.results&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("button",{onClick:()=>{s.push("/scan/".concat(t,"/results"))},className:"flex items-center space-x-2 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors",children:[(0,a.jsx)(N.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"View Results"})]}),(0,a.jsxs)("button",{onClick:()=>{if(!(null==c?void 0:c.results))return;let e=new Blob([JSON.stringify(JSON.parse(c.results),null,2)],{type:"application/json"}),s=URL.createObjectURL(e),a=document.createElement("a");a.href=s,a.download="nuclei-scan-".concat(t,"-results.json"),document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(s)},className:"flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Download"})]})]})})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Nuclei Scan #",t]}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:["Target: ",(0,a.jsx)("span",{className:"font-medium",children:c.target})]}),(0,a.jsxs)("span",{children:["Templates: ",(0,a.jsx)("span",{className:"font-medium",children:c.templates})]}),(0,a.jsxs)("span",{children:["Started: ",(0,a.jsx)("span",{className:"font-medium",children:new Date(c.created_at).toLocaleString()})]})]})]})]}),(0,a.jsx)(j,{scanId:t,onComplete:()=>{m()},onError:e=>{x(e),m()}})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Scan Not Found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"The requested scan could not be found."}),(0,a.jsx)("button",{onClick:()=>s.push("/dashboard"),className:"bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700",children:"Back to Dashboard"})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[80,441,684,358],()=>s(2576)),_N_E=e.O()}]);