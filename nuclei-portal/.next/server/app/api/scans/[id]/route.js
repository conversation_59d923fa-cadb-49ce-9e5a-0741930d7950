(()=>{var e={};e.id=334,e.ids=[334],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2909:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{u:()=>u});var a=s(3205),n=s.n(a),i=s(3139),o=s(6710),l=e([i,o]);[i,o]=l.then?(await l)():l;let c=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-this-in-production";class u{static async login(e,t){let s=await o.db.getUserByEmail(e);if(!s||!await i.default.compare(t,s.password))return null;let r={id:s.id,email:s.email,plan:s.plan,scans_used:s.scans_used,scans_limit:s.scans_limit},a=n().sign({userId:s.id,email:s.email},c,{expiresIn:"7d"});return{user:r,token:a}}static async register(e,t){try{if(await o.db.getUserByEmail(e))return null;let s=await o.db.createUser(e,t),r={id:s.id,email:s.email,plan:s.plan,scans_used:s.scans_used,scans_limit:s.scans_limit},a=n().sign({userId:s.id,email:s.email},c,{expiresIn:"7d"});return{user:r,token:a}}catch{return console.error("Registration error"),null}}static async verifyToken(e){try{let t=n().verify(e,c),s=await o.db.getUserById(t.userId);if(!s)return null;return{id:s.id,email:s.email,plan:s.plan,scans_used:s.scans_used,scans_limit:s.scans_limit}}catch(e){return null}}static extractTokenFromHeader(e){return e&&e.startsWith("Bearer ")?e.substring(7):null}}r()}catch(e){r(e)}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3139:e=>{"use strict";e.exports=import("bcryptjs")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3762:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>c,routeModule:()=>u,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var a=s(6559),n=s(8088),i=s(7719),o=s(5016),l=e([o]);o=(l.then?(await l)():l)[0];let u=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/scans/[id]/route",pathname:"/api/scans/[id]",filename:"route",bundlePath:"app/api/scans/[id]/route"},resolvedPagePath:"/root/nuclei/nuclei-portal/src/app/api/scans/[id]/route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:g}=u;function c(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}r()}catch(e){r(e)}})},3873:e=>{"use strict";e.exports=require("path")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5016:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{DELETE:()=>u,GET:()=>c});var a=s(2190),n=s(2909),i=s(6710),o=s(6868),l=e([n,i,o]);async function c(e,{params:t}){try{let s=e.headers.get("authorization"),r=n.u.extractTokenFromHeader(s);if(!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let l=await n.u.verifyToken(r);if(!l)return a.NextResponse.json({error:"Invalid token"},{status:401});let c=await t,u=parseInt(c.id),d=await i.db.getScanById(u);if(!d)return a.NextResponse.json({error:"Scan not found"},{status:404});if(d.user_id!==l.id)return a.NextResponse.json({error:"Forbidden"},{status:403});let p=await o.q.getScanProgress(u);return a.NextResponse.json({scan:{id:d.id,target:d.target,status:d.status,templates:d.templates,created_at:d.created_at,completed_at:d.completed_at,results:d.results?JSON.parse(d.results):[]},progress:p})}catch(e){return console.error("Get scan error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function u(e,{params:t}){try{let s=e.headers.get("authorization"),r=n.u.extractTokenFromHeader(s);if(!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let l=await n.u.verifyToken(r);if(!l)return a.NextResponse.json({error:"Invalid token"},{status:401});let c=await t,u=parseInt(c.id),d=await i.db.getScanById(u);if(!d)return a.NextResponse.json({error:"Scan not found"},{status:404});if(d.user_id!==l.id)return a.NextResponse.json({error:"Forbidden"},{status:403});return"running"===d.status&&await o.q.stopScan(u),a.NextResponse.json({message:"Scan stopped successfully"})}catch(e){return console.error("Stop scan error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}[n,i,o]=l.then?(await l)():l,r()}catch(e){r(e)}})},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},6710:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{db:()=>d});var a=s(3139),n=e([a]);a=(n.then?(await n)():n)[0];let i=[],o=[],l=1,c=1;class u{constructor(){this.initDefaultData()}async initDefaultData(){if(!i.find(e=>"<EMAIL>"===e.email)){let e=await a.default.hash("admin123",10);i.push({id:l++,email:"<EMAIL>",password:e,plan:"enterprise",scans_used:0,scans_limit:1e3,created_at:new Date().toISOString()}),console.log("Default admin user created: <EMAIL> / admin123")}}async createUser(e,t){let s=await a.default.hash(t,10),r={id:l++,email:e,password:s,plan:"free",scans_used:0,scans_limit:10,created_at:new Date().toISOString()};return i.push(r),r}async getUserByEmail(e){return i.find(t=>t.email===e)||null}async getUserById(e){return i.find(t=>t.id===e)||null}async createScan(e,t,s){let r={id:c++,user_id:e,target:t,status:"pending",templates:s,results:"",created_at:new Date().toISOString()};return o.push(r),r}async updateScan(e,t){let s=o.findIndex(t=>t.id===e);-1!==s&&(o[s]={...o[s],...t})}async getUserScans(e){return o.filter(t=>t.user_id===e).sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime())}async getScanById(e){return o.find(t=>t.id===e)||null}async incrementUserScans(e){let t=i.findIndex(t=>t.id===e);-1!==t&&i[t].scans_used++}}let d=new u;r()}catch(e){r(e)}})},6868:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{q:()=>g});var a=s(9646),n=s(9021),i=s(3873),o=s.n(i),l=s(6710),c=e([l]);function u(e,t){if("undefined"!=typeof global&&global.io&&global.scanProgress){let s={...global.scanProgress.get(e)||{scanId:e,status:"running",progress:0,vulnerabilitiesFound:0,logs:[]},...t};global.scanProgress.set(e,s),global.io.to(`scan-${e}`).emit("scan-progress",s),console.log(`Progress update sent for scan ${e}:`,s)}}function d(e,t){if("undefined"!=typeof global&&global.scanProgress){let s=global.scanProgress.get(e);s&&(s.logs=s.logs||[],s.logs.push(`${new Date().toISOString()}: ${t}`),s.logs.length>50&&(s.logs=s.logs.slice(-50)),u(e,{logs:s.logs}))}}l=(c.then?(await c)():c)[0];class p{async startScan(e,t,s="all",r){console.log(`Starting scan ${e} for target: ${t} with templates: ${s}`);let i=Date.now();try{await l.db.updateScan(e,{status:"running"}),r&&this.scanProgressCallbacks.set(e,r),u(e,{scanId:e,status:"running",progress:0,vulnerabilitiesFound:0,currentTarget:t,timeElapsed:0,logs:[`Scan started for target: ${t}`]});let c=o().join("/app/reports",e.toString());await n.promises.mkdir(c,{recursive:!0});let p=o().join(c,"results.json");console.log(`Output directory: ${c}`),console.log(`Output file: ${p}`),d(e,`Output directory created: ${c}`);let g=["-u",t,"-jsonl","-o",p,"-silent","-timeout","10"];"basic"===s&&g.push("-s","critical,high,medium"),console.log(`Nuclei command: nuclei ${g.join(" ")}`),d(e,`Executing: nuclei ${g.join(" ")}`);let m=(0,a.spawn)("nuclei",g,{stdio:["pipe","pipe","pipe"],env:{...process.env,PATH:process.env.PATH}});this.activeScanProcesses.set(e,m),console.log(`Nuclei process started with PID: ${m.pid}`),d(e,`Nuclei process started with PID: ${m.pid}`);let f=[],h=0,y=0,w=0;m.stdout?.on("data",t=>{for(let s of t.toString().split("\n").filter(e=>e.trim()))try{let t=JSON.parse(s);f.push(t),y++,h=Math.min(h+2,95),console.log(`Nuclei result: ${JSON.stringify(t)}`),d(e,`Vulnerability found: ${t.info?.name||t.template||"Unknown"}`),u(e,{vulnerabilitiesFound:y,currentTemplate:t.template||t.template_id||"Unknown",progress:h,timeElapsed:Math.floor((Date.now()-i)/1e3)}),this.notifyProgress(e,{scanId:e,status:"running",progress:h,results:[...f]})}catch{let t=s.trim();t&&(console.log(`Nuclei stdout: ${t}`),(t.includes("template")||t.includes("Template"))&&(w++,u(e,{currentTemplate:t,progress:Math.min(95,w),timeElapsed:Math.floor((Date.now()-i)/1e3)})),d(e,t))}}),m.stderr?.on("data",t=>{let s=t.toString();console.log(`Nuclei stderr [${e}]: ${s}`),d(e,`Nuclei: ${s.trim()}`),(s.includes("no templates found")||s.includes("template not found"))&&(console.error(`Template error for scan ${e}: ${s}`),d(e,`ERROR: ${s.trim()}`))}),m.on("close",async(t,s)=>{if(console.log(`Nuclei process [${e}] closed with code: ${t}, signal: ${s}`),this.activeScanProcesses.delete(e),this.scanProgressCallbacks.delete(e),0===t){console.log(`Scan ${e} completed successfully with ${f.length} results`);let t=Math.floor((Date.now()-i)/1e3);d(e,`Scan completed successfully with ${f.length} vulnerabilities found`),u(e,{status:"completed",progress:100,vulnerabilitiesFound:f.length,timeElapsed:t,results:f}),await l.db.updateScan(e,{status:"completed",results:JSON.stringify(f),completed_at:new Date().toISOString()}),this.notifyProgress(e,{scanId:e,status:"completed",progress:100,results:f})}else{console.error(`Scan ${e} failed with exit code: ${t}`);let s=Math.floor((Date.now()-i)/1e3),r=`Nuclei process exited with code ${t}`;d(e,`ERROR: ${r}`),u(e,{status:"failed",progress:0,timeElapsed:s,error:r}),await l.db.updateScan(e,{status:"failed",completed_at:new Date().toISOString()}),this.notifyProgress(e,{scanId:e,status:"failed",progress:0,results:[],error:r})}}),m.on("error",async t=>{console.error("Nuclei process error:",t),this.activeScanProcesses.delete(e),this.scanProgressCallbacks.delete(e),await l.db.updateScan(e,{status:"failed",completed_at:new Date().toISOString()}),this.notifyProgress(e,{scanId:e,status:"failed",progress:0,results:[],error:t.message})})}catch(t){console.error("Error starting scan:",t),await l.db.updateScan(e,{status:"failed",completed_at:new Date().toISOString()}),this.notifyProgress(e,{scanId:e,status:"failed",progress:0,results:[],error:t instanceof Error?t.message:"Unknown error"})}}notifyProgress(e,t){let s=this.scanProgressCallbacks.get(e);s&&s(t)}async stopScan(e){let t=this.activeScanProcesses.get(e);t&&(t.kill("SIGTERM"),this.activeScanProcesses.delete(e),this.scanProgressCallbacks.delete(e),await l.db.updateScan(e,{status:"failed",completed_at:new Date().toISOString()}))}async getScanProgress(e){let t=await l.db.getScanById(e);if(!t)return null;let s=t.results?JSON.parse(t.results):[];return{scanId:e,status:t.status,progress:"completed"===t.status?100:50*("running"===t.status),results:s}}isNucleiAvailable(){try{let{execSync:e}=s(9646);return e("nuclei -version",{stdio:"ignore"}),!0}catch{return!1}}constructor(){this.activeScanProcesses=new Map,this.scanProgressCallbacks=new Map}}let g=new p;r()}catch(e){r(e)}})},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9646:e=>{"use strict";e.exports=require("child_process")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[719,580,205],()=>s(3762));module.exports=r})();