(()=>{var e={};e.id=732,e.ids=[732],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2909:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{u:()=>l});var a=s(3205),i=s.n(a),n=s(3139),o=s(6710),c=e([n,o]);[n,o]=c.then?(await c)():c;let u=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-this-in-production";class l{static async login(e,t){let s=await o.db.getUserByEmail(e);if(!s||!await n.default.compare(t,s.password))return null;let r={id:s.id,email:s.email,plan:s.plan,scans_used:s.scans_used,scans_limit:s.scans_limit},a=i().sign({userId:s.id,email:s.email},u,{expiresIn:"7d"});return{user:r,token:a}}static async register(e,t){try{if(await o.db.getUserByEmail(e))return null;let s=await o.db.createUser(e,t),r={id:s.id,email:s.email,plan:s.plan,scans_used:s.scans_used,scans_limit:s.scans_limit},a=i().sign({userId:s.id,email:s.email},u,{expiresIn:"7d"});return{user:r,token:a}}catch{return console.error("Registration error"),null}}static async verifyToken(e){try{let t=i().verify(e,u),s=await o.db.getUserById(t.userId);if(!s)return null;return{id:s.id,email:s.email,plan:s.plan,scans_used:s.scans_used,scans_limit:s.scans_limit}}catch(e){return null}}static extractTokenFromHeader(e){return e&&e.startsWith("Bearer ")?e.substring(7):null}}r()}catch(e){r(e)}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3139:e=>{"use strict";e.exports=import("bcryptjs")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4872:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>u,routeModule:()=>l,serverHooks:()=>E,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var a=s(6559),i=s(8088),n=s(7719),o=s(5004),c=e([o]);o=(c.then?(await c)():c)[0];let l=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/scans/route",pathname:"/api/scans",filename:"route",bundlePath:"app/api/scans/route"},resolvedPagePath:"/root/nuclei/nuclei-portal/src/app/api/scans/route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:E}=l;function u(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}r()}catch(e){r(e)}})},5004:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{GET:()=>u,POST:()=>l});var a=s(2190),i=s(2909),n=s(6710),o=s(6868),c=e([i,n,o]);async function u(e){try{let t=e.headers.get("authorization"),s=i.u.extractTokenFromHeader(t);if(!s)return a.NextResponse.json({error:"Unauthorized"},{status:401});let r=await i.u.verifyToken(s);if(!r)return a.NextResponse.json({error:"Invalid token"},{status:401});let o=await n.db.getUserScans(r.id);return a.NextResponse.json({scans:o})}catch(e){return console.error("Get scans error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(e){try{let t=e.headers.get("authorization"),s=i.u.extractTokenFromHeader(t);if(!s)return a.NextResponse.json({error:"Unauthorized"},{status:401});let r=await i.u.verifyToken(s);if(!r)return a.NextResponse.json({error:"Invalid token"},{status:401});if(r.scans_used>=r.scans_limit)return a.NextResponse.json({error:"Scan limit reached. Please upgrade your plan."},{status:403});let{target:c,templates:u="basic"}=await e.json();if(!c)return a.NextResponse.json({error:"Target is required"},{status:400});if(!/^https?:\/\/.+/.test(c)&&!/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(c)&&!/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/.test(c))return a.NextResponse.json({error:"Invalid target format. Use URL, IP address, or domain name."},{status:400});let l=await n.db.createScan(r.id,c,u);return await n.db.incrementUserScans(r.id),o.q.startScan(l.id,c,u),a.NextResponse.json({scan:{id:l.id,target:l.target,status:l.status,templates:l.templates,created_at:l.created_at}})}catch(e){return console.error("Create scan error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}[i,n,o]=c.then?(await c)():c,r()}catch(e){r(e)}})},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},6689:e=>{"use strict";e.exports=require("sqlite3")},6710:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{db:()=>u});var a=s(6689),i=s(8354),n=s(3139),o=e([n]);n=(o.then?(await o)():o)[0];class c{constructor(){this.db=new a.Database("./database/nuclei_portal.db"),this.initTables()}async initTables(){let e=(0,i.promisify)(this.db.run.bind(this.db));await e(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        plan TEXT DEFAULT 'free',
        scans_used INTEGER DEFAULT 0,
        scans_limit INTEGER DEFAULT 10,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `),await e(`
      CREATE TABLE IF NOT EXISTS scans (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        target TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        templates TEXT,
        results TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        completed_at DATETIME,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `),await this.createDefaultAdmin()}async createDefaultAdmin(){let e=(0,i.promisify)(this.db.get.bind(this.db)),t=(0,i.promisify)(this.db.run.bind(this.db));if(!await e("SELECT id FROM users WHERE email = ?",["<EMAIL>"])){let e=await n.default.hash("admin123",10);await t("INSERT INTO users (email, password, plan, scans_limit) VALUES (?, ?, ?, ?)",["<EMAIL>",e,"enterprise",1e3]),console.log("Default admin user created: <EMAIL> / admin123")}}async createUser(e,t){let s=(0,i.promisify)(this.db.run.bind(this.db)),r=(0,i.promisify)(this.db.get.bind(this.db)),a=await n.default.hash(t,10),o=await s("INSERT INTO users (email, password) VALUES (?, ?)",[e,a]);return await r("SELECT * FROM users WHERE id = ?",[o.lastID])}async getUserByEmail(e){let t=(0,i.promisify)(this.db.get.bind(this.db));return await t("SELECT * FROM users WHERE email = ?",[e])}async getUserById(e){let t=(0,i.promisify)(this.db.get.bind(this.db));return await t("SELECT * FROM users WHERE id = ?",[e])}async createScan(e,t,s){let r=(0,i.promisify)(this.db.run.bind(this.db)),a=(0,i.promisify)(this.db.get.bind(this.db)),n=await r("INSERT INTO scans (user_id, target, templates) VALUES (?, ?, ?)",[e,t,s]);return await a("SELECT * FROM scans WHERE id = ?",[n.lastID])}async updateScan(e,t){let s=(0,i.promisify)(this.db.run.bind(this.db)),r=Object.keys(t).map(e=>`${e} = ?`).join(", "),a=Object.values(t);await s(`UPDATE scans SET ${r} WHERE id = ?`,[...a,e])}async getUserScans(e){let t=(0,i.promisify)(this.db.all.bind(this.db));return await t("SELECT * FROM scans WHERE user_id = ? ORDER BY created_at DESC",[e])}async getScanById(e){let t=(0,i.promisify)(this.db.get.bind(this.db));return await t("SELECT * FROM scans WHERE id = ?",[e])}async incrementUserScans(e){let t=(0,i.promisify)(this.db.run.bind(this.db));await t("UPDATE users SET scans_used = scans_used + 1 WHERE id = ?",[e])}}let u=new c;r()}catch(e){r(e)}})},6868:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{q:()=>d});var a=s(9646),i=s(9021),n=s(3873),o=s.n(n),c=s(6710),u=e([c]);c=(u.then?(await u)():u)[0];class l{async startScan(e,t,s="all",r){try{await c.db.updateScan(e,{status:"running"}),r&&this.scanProgressCallbacks.set(e,r);let n=o().join(process.cwd(),"reports",e.toString());await i.promises.mkdir(n,{recursive:!0});let u=o().join(n,"results.json"),l=["-target",t,"-json","-o",u,"-stats","-silent"];"all"===s?l.push("-t","/root/nuclei-templates/"):"basic"===s?(l.push("-t","/root/nuclei-templates/cves/"),l.push("-t","/root/nuclei-templates/vulnerabilities/")):l.push("-t",s);let d=(0,a.spawn)("nuclei",l,{stdio:["pipe","pipe","pipe"]});this.activeScanProcesses.set(e,d);let p=[],E=0;d.stdout?.on("data",t=>{for(let s of t.toString().split("\n").filter(e=>e.trim()))try{let t=JSON.parse(s);p.push(t),E=Math.min(E+1,95),this.notifyProgress(e,{scanId:e,status:"running",progress:E,results:[...p]})}catch{}}),d.stderr?.on("data",e=>{console.log(`Nuclei stderr: ${e}`)}),d.on("close",async t=>{this.activeScanProcesses.delete(e),this.scanProgressCallbacks.delete(e),0===t?(await c.db.updateScan(e,{status:"completed",results:JSON.stringify(p),completed_at:new Date().toISOString()}),this.notifyProgress(e,{scanId:e,status:"completed",progress:100,results:p})):(await c.db.updateScan(e,{status:"failed",completed_at:new Date().toISOString()}),this.notifyProgress(e,{scanId:e,status:"failed",progress:0,results:[],error:`Nuclei process exited with code ${t}`}))}),d.on("error",async t=>{console.error("Nuclei process error:",t),this.activeScanProcesses.delete(e),this.scanProgressCallbacks.delete(e),await c.db.updateScan(e,{status:"failed",completed_at:new Date().toISOString()}),this.notifyProgress(e,{scanId:e,status:"failed",progress:0,results:[],error:t.message})})}catch(t){console.error("Error starting scan:",t),await c.db.updateScan(e,{status:"failed",completed_at:new Date().toISOString()}),this.notifyProgress(e,{scanId:e,status:"failed",progress:0,results:[],error:t instanceof Error?t.message:"Unknown error"})}}notifyProgress(e,t){let s=this.scanProgressCallbacks.get(e);s&&s(t)}async stopScan(e){let t=this.activeScanProcesses.get(e);t&&(t.kill("SIGTERM"),this.activeScanProcesses.delete(e),this.scanProgressCallbacks.delete(e),await c.db.updateScan(e,{status:"failed",completed_at:new Date().toISOString()}))}async getScanProgress(e){let t=await c.db.getScanById(e);if(!t)return null;let s=t.results?JSON.parse(t.results):[];return{scanId:e,status:t.status,progress:"completed"===t.status?100:50*("running"===t.status),results:s}}isNucleiAvailable(){try{let{execSync:e}=s(9646);return e("nuclei -version",{stdio:"ignore"}),!0}catch{return!1}}constructor(){this.activeScanProcesses=new Map,this.scanProgressCallbacks=new Map}}let d=new l;r()}catch(e){r(e)}})},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9646:e=>{"use strict";e.exports=require("child_process")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580,205],()=>s(4872));module.exports=r})();