(()=>{var e={};e.id=357,e.ids=[357],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},6559:(e,t,r)=>{"use strict";e.exports=r(4870)},7768:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>l,routeModule:()=>u,serverHooks:()=>d,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{GET:()=>i});var o=r(6559),n=r(8088),a=r(7719);async function i(e){return new Response("WebSocket endpoint",{status:200})}let u=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/socket/route",pathname:"/api/socket",filename:"route",bundlePath:"app/api/socket/route"},resolvedPagePath:"/root/nuclei/nuclei-portal/src/app/api/socket/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:c,serverHooks:d}=u;function l(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:c})}},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[719],()=>r(7768));module.exports=s})();