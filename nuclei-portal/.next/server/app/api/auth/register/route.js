(()=>{var e={};e.id=612,e.ids=[612],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2909:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{u:()=>o});var a=r(3205),n=r.n(a),i=r(3139),u=r(6710),c=e([i,u]);[i,u]=c.then?(await c)():c;let l=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-this-in-production";class o{static async login(e,t){let r=await u.db.getUserByEmail(e);if(!r||!await i.default.compare(t,r.password))return null;let s={id:r.id,email:r.email,plan:r.plan,scans_used:r.scans_used,scans_limit:r.scans_limit},a=n().sign({userId:r.id,email:r.email},l,{expiresIn:"7d"});return{user:s,token:a}}static async register(e,t){try{if(await u.db.getUserByEmail(e))return null;let r=await u.db.createUser(e,t),s={id:r.id,email:r.email,plan:r.plan,scans_used:r.scans_used,scans_limit:r.scans_limit},a=n().sign({userId:r.id,email:r.email},l,{expiresIn:"7d"});return{user:s,token:a}}catch{return console.error("Registration error"),null}}static async verifyToken(e){try{let t=n().verify(e,l),r=await u.db.getUserById(t.userId);if(!r)return null;return{id:r.id,email:r.email,plan:r.plan,scans_used:r.scans_used,scans_limit:r.scans_limit}}catch(e){return null}}static extractTokenFromHeader(e){return e&&e.startsWith("Bearer ")?e.substring(7):null}}s()}catch(e){s(e)}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3139:e=>{"use strict";e.exports=import("bcryptjs")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4360:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>l,routeModule:()=>o,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var a=r(6559),n=r(8088),i=r(7719),u=r(6264),c=e([u]);u=(c.then?(await c)():c)[0];let o=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"/root/nuclei/nuclei-portal/src/app/api/auth/register/route.ts",nextConfigOutput:"standalone",userland:u}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:m}=o;function l(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}s()}catch(e){s(e)}})},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6264:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{POST:()=>u});var a=r(2190),n=r(2909),i=e([n]);async function u(e){try{let{email:t,password:r}=await e.json();if(!t||!r)return a.NextResponse.json({error:"Email and password are required"},{status:400});if(r.length<6)return a.NextResponse.json({error:"Password must be at least 6 characters long"},{status:400});let s=await n.u.register(t,r);if(!s)return a.NextResponse.json({error:"User already exists or registration failed"},{status:409});return a.NextResponse.json({user:s.user,token:s.token})}catch(e){return console.error("Registration error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}n=(i.then?(await i)():i)[0],s()}catch(e){s(e)}})},6487:()=>{},6710:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{db:()=>d});var a=r(3139),n=e([a]);a=(n.then?(await n)():n)[0];let i=[],u=[],c=1,l=1;class o{constructor(){this.initDefaultData()}async initDefaultData(){if(!i.find(e=>"<EMAIL>"===e.email)){let e=await a.default.hash("admin123",10);i.push({id:c++,email:"<EMAIL>",password:e,plan:"enterprise",scans_used:0,scans_limit:1e3,created_at:new Date().toISOString()}),console.log("Default admin user created: <EMAIL> / admin123")}}async createUser(e,t){let r=await a.default.hash(t,10),s={id:c++,email:e,password:r,plan:"free",scans_used:0,scans_limit:10,created_at:new Date().toISOString()};return i.push(s),s}async getUserByEmail(e){return i.find(t=>t.email===e)||null}async getUserById(e){return i.find(t=>t.id===e)||null}async createScan(e,t,r){let s={id:l++,user_id:e,target:t,status:"pending",templates:r,results:"",created_at:new Date().toISOString()};return u.push(s),s}async updateScan(e,t){let r=u.findIndex(t=>t.id===e);-1!==r&&(u[r]={...u[r],...t})}async getUserScans(e){return u.filter(t=>t.user_id===e).sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime())}async getScanById(e){return u.find(t=>t.id===e)||null}async incrementUserScans(e){let t=i.findIndex(t=>t.id===e);-1!==t&&i[t].scans_used++}}let d=new o;s()}catch(e){s(e)}})},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[719,580,205],()=>r(4360));module.exports=s})();