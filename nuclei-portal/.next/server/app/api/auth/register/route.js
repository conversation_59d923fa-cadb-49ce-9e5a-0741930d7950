(()=>{var e={};e.id=612,e.ids=[612],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2909:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{u:()=>l});var a=s(3205),i=s.n(a),n=s(3139),u=s(6710),d=e([n,u]);[n,u]=d.then?(await d)():d;let c=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-this-in-production";class l{static async login(e,t){let s=await u.db.getUserByEmail(e);if(!s||!await n.default.compare(t,s.password))return null;let r={id:s.id,email:s.email,plan:s.plan,scans_used:s.scans_used,scans_limit:s.scans_limit},a=i().sign({userId:s.id,email:s.email},c,{expiresIn:"7d"});return{user:r,token:a}}static async register(e,t){try{if(await u.db.getUserByEmail(e))return null;let s=await u.db.createUser(e,t),r={id:s.id,email:s.email,plan:s.plan,scans_used:s.scans_used,scans_limit:s.scans_limit},a=i().sign({userId:s.id,email:s.email},c,{expiresIn:"7d"});return{user:r,token:a}}catch{return console.error("Registration error"),null}}static async verifyToken(e){try{let t=i().verify(e,c),s=await u.db.getUserById(t.userId);if(!s)return null;return{id:s.id,email:s.email,plan:s.plan,scans_used:s.scans_used,scans_limit:s.scans_limit}}catch(e){return null}}static extractTokenFromHeader(e){return e&&e.startsWith("Bearer ")?e.substring(7):null}}r()}catch(e){r(e)}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3139:e=>{"use strict";e.exports=import("bcryptjs")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4360:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>c,routeModule:()=>l,serverHooks:()=>p,workAsyncStorage:()=>o,workUnitAsyncStorage:()=>E});var a=s(6559),i=s(8088),n=s(7719),u=s(6264),d=e([u]);u=(d.then?(await d)():d)[0];let l=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"/root/nuclei/nuclei-portal/src/app/api/auth/register/route.ts",nextConfigOutput:"standalone",userland:u}),{workAsyncStorage:o,workUnitAsyncStorage:E,serverHooks:p}=l;function c(){return(0,n.patchFetch)({workAsyncStorage:o,workUnitAsyncStorage:E})}r()}catch(e){r(e)}})},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6264:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{POST:()=>u});var a=s(2190),i=s(2909),n=e([i]);async function u(e){try{let{email:t,password:s}=await e.json();if(!t||!s)return a.NextResponse.json({error:"Email and password are required"},{status:400});if(s.length<6)return a.NextResponse.json({error:"Password must be at least 6 characters long"},{status:400});let r=await i.u.register(t,s);if(!r)return a.NextResponse.json({error:"User already exists or registration failed"},{status:409});return a.NextResponse.json({user:r.user,token:r.token})}catch(e){return console.error("Registration error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}i=(n.then?(await n)():n)[0],r()}catch(e){r(e)}})},6487:()=>{},6689:e=>{"use strict";e.exports=require("sqlite3")},6710:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{db:()=>c});var a=s(6689),i=s(8354),n=s(3139),u=e([n]);n=(u.then?(await u)():u)[0];class d{constructor(){this.db=new a.Database("./database/nuclei_portal.db"),this.initTables()}async initTables(){let e=(0,i.promisify)(this.db.run.bind(this.db));await e(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        plan TEXT DEFAULT 'free',
        scans_used INTEGER DEFAULT 0,
        scans_limit INTEGER DEFAULT 10,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `),await e(`
      CREATE TABLE IF NOT EXISTS scans (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        target TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        templates TEXT,
        results TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        completed_at DATETIME,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `),await this.createDefaultAdmin()}async createDefaultAdmin(){let e=(0,i.promisify)(this.db.get.bind(this.db)),t=(0,i.promisify)(this.db.run.bind(this.db));if(!await e("SELECT id FROM users WHERE email = ?",["<EMAIL>"])){let e=await n.default.hash("admin123",10);await t("INSERT INTO users (email, password, plan, scans_limit) VALUES (?, ?, ?, ?)",["<EMAIL>",e,"enterprise",1e3]),console.log("Default admin user created: <EMAIL> / admin123")}}async createUser(e,t){let s=(0,i.promisify)(this.db.run.bind(this.db)),r=(0,i.promisify)(this.db.get.bind(this.db)),a=await n.default.hash(t,10),u=await s("INSERT INTO users (email, password) VALUES (?, ?)",[e,a]);return await r("SELECT * FROM users WHERE id = ?",[u.lastID])}async getUserByEmail(e){let t=(0,i.promisify)(this.db.get.bind(this.db));return await t("SELECT * FROM users WHERE email = ?",[e])}async getUserById(e){let t=(0,i.promisify)(this.db.get.bind(this.db));return await t("SELECT * FROM users WHERE id = ?",[e])}async createScan(e,t,s){let r=(0,i.promisify)(this.db.run.bind(this.db)),a=(0,i.promisify)(this.db.get.bind(this.db)),n=await r("INSERT INTO scans (user_id, target, templates) VALUES (?, ?, ?)",[e,t,s]);return await a("SELECT * FROM scans WHERE id = ?",[n.lastID])}async updateScan(e,t){let s=(0,i.promisify)(this.db.run.bind(this.db)),r=Object.keys(t).map(e=>`${e} = ?`).join(", "),a=Object.values(t);await s(`UPDATE scans SET ${r} WHERE id = ?`,[...a,e])}async getUserScans(e){let t=(0,i.promisify)(this.db.all.bind(this.db));return await t("SELECT * FROM scans WHERE user_id = ? ORDER BY created_at DESC",[e])}async getScanById(e){let t=(0,i.promisify)(this.db.get.bind(this.db));return await t("SELECT * FROM scans WHERE id = ?",[e])}async incrementUserScans(e){let t=(0,i.promisify)(this.db.run.bind(this.db));await t("UPDATE users SET scans_used = scans_used + 1 WHERE id = ?",[e])}}let c=new d;r()}catch(e){r(e)}})},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580,205],()=>s(4360));module.exports=r})();