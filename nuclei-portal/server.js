const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const { Server } = require('socket.io');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = process.env.PORT || 3000;

const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

// Store for scan progress
const scanProgress = new Map();

app.prepare().then(() => {
  const server = createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  });

  // Initialize Socket.IO
  const io = new Server(server, {
    path: '/api/socket',
    cors: {
      origin: "*",
      methods: ["GET", "POST"]
    }
  });

  io.on('connection', (socket) => {
    console.log('Client connected:', socket.id);

    // Join scan room
    socket.on('join-scan', (scanId) => {
      socket.join(`scan-${scanId}`);
      console.log(`Client ${socket.id} joined scan room: scan-${scanId}`);
      
      // Send current progress if available
      const progress = scanProgress.get(scanId);
      if (progress) {
        socket.emit('scan-progress', progress);
      }
    });

    // Leave scan room
    socket.on('leave-scan', (scanId) => {
      socket.leave(`scan-${scanId}`);
      console.log(`Client ${socket.id} left scan room: scan-${scanId}`);
    });

    socket.on('disconnect', () => {
      console.log('Client disconnected:', socket.id);
    });
  });

  // Make io available globally for the Nuclei service
  global.io = io;
  global.scanProgress = scanProgress;

  server
    .once('error', (err) => {
      console.error(err);
      process.exit(1);
    })
    .listen(port, () => {
      console.log(`> Ready on http://${hostname}:${port}`);
    });
});
