# Nuclei Portal - Professional Vulnerability Scanner

Un portal web moderno para ejecutar Nuclei de forma gráfica y generar informes HTML profesionales.

## Características

- 🔐 **Autenticación segura** con JWT
- 🎯 **Interfaz gráfica moderna** para configurar scans
- 📊 **Dashboard en tiempo real** para monitorear scans
- 📄 **Generación de reportes HTML** profesionales
- 🐳 **Completamente dockerizado** con Nuclei preinstalado
- 💼 **Características comerciales** con planes de suscripción
- 🔒 **Seguridad robusta** con límites de uso y validación

## Inicio Rápido

### Usando Docker (Recomendado)

```bash
# Construir la imagen
docker build -t nuclei-portal .

# Ejecutar con docker-compose
docker-compose up -d

# O ejecutar directamente
docker run -p 3000:3000 -v nuclei_data:/app/data nuclei-portal
```

### Desarrollo Local

```bash
# Instalar dependencias
npm install

# Ejecutar en modo desarrollo
npm run dev
```

## Credenciales por Defecto

- **Email:** <EMAIL>
- **Password:** admin123

## APIs Disponibles

### Autenticación
- `POST /api/auth/login` - Iniciar sesión
- `POST /api/auth/register` - Registrar usuario

### Scans
- `GET /api/scans` - Listar scans del usuario
- `POST /api/scans` - Crear nuevo scan
- `GET /api/scans/[id]` - Obtener detalles de scan
- `DELETE /api/scans/[id]` - Detener scan

## Planes de Suscripción

### Free
- 10 scans por mes
- Templates básicos
- Reportes estándar

### Pro
- 100 scans por mes
- Todos los templates
- Reportes premium

### Enterprise
- 1000 scans por mes
- Templates personalizados
- API access

---

**Nuclei Portal** - Haciendo la seguridad accesible para todos 🛡️
