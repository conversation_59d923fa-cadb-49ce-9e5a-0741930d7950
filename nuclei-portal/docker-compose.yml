version: '3.8'

services:
  nuclei-portal:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=file:./database.sqlite
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - NEXT_PUBLIC_API_URL=http://localhost:3000
    volumes:
      - nuclei_data:/app/data
      - nuclei_reports:/app/reports
      - nuclei_db:/app/database
    restart: unless-stopped
    networks:
      - nuclei-network

  # Optional: Add a database service for production
  # postgres:
  #   image: postgres:15-alpine
  #   environment:
  #     POSTGRES_DB: nuclei_portal
  #     POSTGRES_USER: nuclei_user
  #     POSTGRES_PASSWORD: nuclei_password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - nuclei-network

volumes:
  nuclei_data:
  nuclei_reports:
  nuclei_db:
  # postgres_data:

networks:
  nuclei-network:
    driver: bridge
