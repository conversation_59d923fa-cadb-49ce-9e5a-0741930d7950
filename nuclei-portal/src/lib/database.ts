import bcrypt from 'bcryptjs';

interface User {
  id: number;
  email: string;
  password: string;
  plan: 'free' | 'pro' | 'enterprise';
  scans_used: number;
  scans_limit: number;
  created_at: string;
}

interface Scan {
  id: number;
  user_id: number;
  target: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  templates: string;
  results: string;
  created_at: string;
  completed_at?: string;
}

// In-memory storage for demo purposes
const users: User[] = [];
const scans: Scan[] = [];
let nextUserId = 1;
let nextScanId = 1;

class DatabaseManager {
  constructor() {
    this.initDefaultData();
  }

  private async initDefaultData() {
    // Create admin user if not exists
    const adminExists = users.find(u => u.email === '<EMAIL>');

    if (!adminExists) {
      const hashedPassword = await bcrypt.hash('admin123', 10);
      users.push({
        id: nextUserId++,
        email: '<EMAIL>',
        password: hashedPassword,
        plan: 'enterprise',
        scans_used: 0,
        scans_limit: 1000,
        created_at: new Date().toISOString()
      });
      console.log('Default admin user created: <EMAIL> / admin123');
    }
  }



  async createUser(email: string, password: string): Promise<User> {
    const hashedPassword = await bcrypt.hash(password, 10);
    const user: User = {
      id: nextUserId++,
      email,
      password: hashedPassword,
      plan: 'free',
      scans_used: 0,
      scans_limit: 10,
      created_at: new Date().toISOString()
    };

    users.push(user);
    return user;
  }

  async getUserByEmail(email: string): Promise<User | null> {
    return users.find(u => u.email === email) || null;
  }

  async getUserById(id: number): Promise<User | null> {
    return users.find(u => u.id === id) || null;
  }

  async createScan(userId: number, target: string, templates: string): Promise<Scan> {
    const scan: Scan = {
      id: nextScanId++,
      user_id: userId,
      target,
      status: 'pending',
      templates,
      results: '',
      created_at: new Date().toISOString()
    };

    scans.push(scan);
    return scan;
  }

  async updateScan(id: number, updates: Partial<Scan>): Promise<void> {
    const scanIndex = scans.findIndex(s => s.id === id);
    if (scanIndex !== -1) {
      scans[scanIndex] = { ...scans[scanIndex], ...updates };
    }
  }

  async getUserScans(userId: number): Promise<Scan[]> {
    return scans.filter(s => s.user_id === userId).sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  }

  async getScanById(id: number): Promise<Scan | null> {
    return scans.find(s => s.id === id) || null;
  }

  async incrementUserScans(userId: number): Promise<void> {
    const userIndex = users.findIndex(u => u.id === userId);
    if (userIndex !== -1) {
      users[userIndex].scans_used++;
    }
  }
}

export const db = new DatabaseManager();
export type { User, Scan };
