import { Database } from 'sqlite3';
import { promisify } from 'util';
import bcrypt from 'bcryptjs';

interface User {
  id: number;
  email: string;
  password: string;
  plan: 'free' | 'pro' | 'enterprise';
  scans_used: number;
  scans_limit: number;
  created_at: string;
}

interface Scan {
  id: number;
  user_id: number;
  target: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  templates: string;
  results: string;
  created_at: string;
  completed_at?: string;
}

class DatabaseManager {
  private db: Database;

  constructor() {
    this.db = new Database('./database/nuclei_portal.db');
    this.initTables();
  }

  private async initTables() {
    const run = promisify(this.db.run.bind(this.db)) as (sql: string, params?: any[]) => Promise<any>;
    
    // Users table
    await run(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        plan TEXT DEFAULT 'free',
        scans_used INTEGER DEFAULT 0,
        scans_limit INTEGER DEFAULT 10,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Scans table
    await run(`
      CREATE TABLE IF NOT EXISTS scans (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        target TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        templates TEXT,
        results TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        completed_at DATETIME,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Create admin user if not exists
    await this.createDefaultAdmin();
  }

  private async createDefaultAdmin() {
    const get = promisify(this.db.get.bind(this.db)) as (sql: string, params?: any[]) => Promise<any>;
    const run = promisify(this.db.run.bind(this.db)) as (sql: string, params?: any[]) => Promise<any>;

    const adminExists = await get('SELECT id FROM users WHERE email = ?', ['<EMAIL>']);
    
    if (!adminExists) {
      const hashedPassword = await bcrypt.hash('admin123', 10);
      await run(
        'INSERT INTO users (email, password, plan, scans_limit) VALUES (?, ?, ?, ?)',
        ['<EMAIL>', hashedPassword, 'enterprise', 1000]
      );
      console.log('Default admin user created: <EMAIL> / admin123');
    }
  }

  async createUser(email: string, password: string): Promise<User> {
    const run = promisify(this.db.run.bind(this.db)) as (sql: string, params?: any[]) => Promise<any>;
    const get = promisify(this.db.get.bind(this.db)) as (sql: string, params?: any[]) => Promise<any>;

    const hashedPassword = await bcrypt.hash(password, 10);
    const result = await run(
      'INSERT INTO users (email, password) VALUES (?, ?)',
      [email, hashedPassword]
    );

    return await get('SELECT * FROM users WHERE id = ?', [result.lastID]) as User;
  }

  async getUserByEmail(email: string): Promise<User | null> {
    const get = promisify(this.db.get.bind(this.db)) as (sql: string, params?: any[]) => Promise<any>;
    return await get('SELECT * FROM users WHERE email = ?', [email]) as User | null;
  }

  async getUserById(id: number): Promise<User | null> {
    const get = promisify(this.db.get.bind(this.db)) as (sql: string, params?: any[]) => Promise<any>;
    return await get('SELECT * FROM users WHERE id = ?', [id]) as User | null;
  }

  async createScan(userId: number, target: string, templates: string): Promise<Scan> {
    const run = promisify(this.db.run.bind(this.db)) as (sql: string, params?: any[]) => Promise<any>;
    const get = promisify(this.db.get.bind(this.db)) as (sql: string, params?: any[]) => Promise<any>;

    const result = await run(
      'INSERT INTO scans (user_id, target, templates) VALUES (?, ?, ?)',
      [userId, target, templates]
    );

    return await get('SELECT * FROM scans WHERE id = ?', [result.lastID]) as Scan;
  }

  async updateScan(id: number, updates: Partial<Scan>): Promise<void> {
    const run = promisify(this.db.run.bind(this.db)) as (sql: string, params?: any[]) => Promise<any>;
    
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);
    
    await run(`UPDATE scans SET ${fields} WHERE id = ?`, [...values, id]);
  }

  async getUserScans(userId: number): Promise<Scan[]> {
    const all = promisify(this.db.all.bind(this.db)) as (sql: string, params?: any[]) => Promise<any>;
    return await all('SELECT * FROM scans WHERE user_id = ? ORDER BY created_at DESC', [userId]) as Scan[];
  }

  async getScanById(id: number): Promise<Scan | null> {
    const get = promisify(this.db.get.bind(this.db)) as (sql: string, params?: any[]) => Promise<any>;
    return await get('SELECT * FROM scans WHERE id = ?', [id]) as Scan | null;
  }

  async incrementUserScans(userId: number): Promise<void> {
    const run = promisify(this.db.run.bind(this.db)) as (sql: string, params?: any[]) => Promise<any>;
    await run('UPDATE users SET scans_used = scans_used + 1 WHERE id = ?', [userId]);
  }
}

export const db = new DatabaseManager();
export type { User, Scan };
