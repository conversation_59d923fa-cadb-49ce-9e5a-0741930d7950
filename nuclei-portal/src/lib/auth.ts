import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { db } from './database';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

export interface AuthUser {
  id: number;
  email: string;
  plan: string;
  scans_used: number;
  scans_limit: number;
}

export class AuthService {
  static async login(email: string, password: string): Promise<{ user: AuthUser; token: string } | null> {
    const user = await db.getUserByEmail(email);
    
    if (!user) {
      return null;
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    
    if (!isValidPassword) {
      return null;
    }

    const authUser: AuthUser = {
      id: user.id,
      email: user.email,
      plan: user.plan,
      scans_used: user.scans_used,
      scans_limit: user.scans_limit
    };

    const token = jwt.sign(
      { userId: user.id, email: user.email },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    return { user: authUser, token };
  }

  static async register(email: string, password: string): Promise<{ user: AuthUser; token: string } | null> {
    try {
      const existingUser = await db.getUserByEmail(email);
      
      if (existingUser) {
        return null; // User already exists
      }

      const user = await db.createUser(email, password);
      
      const authUser: AuthUser = {
        id: user.id,
        email: user.email,
        plan: user.plan,
        scans_used: user.scans_used,
        scans_limit: user.scans_limit
      };

      const token = jwt.sign(
        { userId: user.id, email: user.email },
        JWT_SECRET,
        { expiresIn: '7d' }
      );

      return { user: authUser, token };
    } catch {
      console.error('Registration error');
      return null;
    }
  }

  static async verifyToken(token: string): Promise<AuthUser | null> {
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as { userId: number; email: string };
      const user = await db.getUserById(decoded.userId);
      
      if (!user) {
        return null;
      }

      return {
        id: user.id,
        email: user.email,
        plan: user.plan,
        scans_used: user.scans_used,
        scans_limit: user.scans_limit
      };
    } catch (error) {
      return null;
    }
  }

  static extractTokenFromHeader(authHeader: string | null | undefined): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  }
}

export const planLimits = {
  free: { scans: 10, templates: 'basic' },
  pro: { scans: 100, templates: 'all' },
  enterprise: { scans: 1000, templates: 'all' }
};
