import { Server as HTTPServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { NextApiRequest } from 'next';
import { Socket as NetSocket } from 'net';

interface SocketServer extends HTTPServer {
  io?: SocketIOServer | undefined;
}

interface SocketWithIO extends NetSocket {
  server: SocketServer;
}

interface NextApiResponseWithSocket extends Response {
  socket: SocketWithIO;
}

export interface ScanProgress {
  scanId: number;
  status: 'running' | 'completed' | 'failed';
  progress: number;
  currentTemplate?: string;
  vulnerabilitiesFound: number;
  totalTemplates?: number;
  currentTarget?: string;
  timeElapsed?: number;
  estimatedTimeRemaining?: number;
  results?: any[];
  error?: string;
  logs?: string[];
}

class WebSocketManager {
  private static instance: WebSocketManager;
  private io: SocketIOServer | null = null;
  private scanProgress: Map<number, ScanProgress> = new Map();

  private constructor() {}

  static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  initializeSocket(server: HTTPServer): SocketIOServer {
    if (!this.io) {
      this.io = new SocketIOServer(server, {
        path: '/api/socket',
        addTrailingSlash: false,
        cors: {
          origin: "*",
          methods: ["GET", "POST"]
        }
      });

      this.io.on('connection', (socket) => {
        console.log('Client connected:', socket.id);

        // Join scan room
        socket.on('join-scan', (scanId: number) => {
          socket.join(`scan-${scanId}`);
          console.log(`Client ${socket.id} joined scan room: scan-${scanId}`);
          
          // Send current progress if available
          const progress = this.scanProgress.get(scanId);
          if (progress) {
            socket.emit('scan-progress', progress);
          }
        });

        // Leave scan room
        socket.on('leave-scan', (scanId: number) => {
          socket.leave(`scan-${scanId}`);
          console.log(`Client ${socket.id} left scan room: scan-${scanId}`);
        });

        socket.on('disconnect', () => {
          console.log('Client disconnected:', socket.id);
        });
      });
    }

    return this.io;
  }

  updateScanProgress(scanId: number, progress: Partial<ScanProgress>) {
    const currentProgress = this.scanProgress.get(scanId) || {
      scanId,
      status: 'running',
      progress: 0,
      vulnerabilitiesFound: 0,
      logs: []
    };

    const updatedProgress = { ...currentProgress, ...progress };
    this.scanProgress.set(scanId, updatedProgress);

    if (this.io) {
      this.io.to(`scan-${scanId}`).emit('scan-progress', updatedProgress);
      console.log(`Progress update sent for scan ${scanId}:`, updatedProgress);
    }
  }

  addScanLog(scanId: number, log: string) {
    const progress = this.scanProgress.get(scanId);
    if (progress) {
      progress.logs = progress.logs || [];
      progress.logs.push(`${new Date().toISOString()}: ${log}`);
      
      // Keep only last 50 logs
      if (progress.logs.length > 50) {
        progress.logs = progress.logs.slice(-50);
      }

      this.updateScanProgress(scanId, { logs: progress.logs });
    }
  }

  getScanProgress(scanId: number): ScanProgress | undefined {
    return this.scanProgress.get(scanId);
  }

  clearScanProgress(scanId: number) {
    this.scanProgress.delete(scanId);
  }

  getIO(): SocketIOServer | null {
    return this.io;
  }
}

export default WebSocketManager;
