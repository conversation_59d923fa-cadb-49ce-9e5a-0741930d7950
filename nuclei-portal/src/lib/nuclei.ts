import { spawn, ChildProcess } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { db } from './database';

// Declare global types for WebSocket
declare global {
  var io: any;
  var scanProgress: Map<number, any>;
}

export interface NucleiResult {
  template: string;
  template_id: string;
  template_path: string;
  info: {
    name: string;
    author: string[];
    tags: string[];
    description: string;
    reference: string[];
    severity: string;
    metadata: Record<string, any>;
  };
  type: string;
  host: string;
  matched_at: string;
  extracted_results?: string[];
  request?: string;
  response?: string;
  curl_command?: string;
  matcher_status: boolean;
  timestamp: string;
}

export interface ScanProgress {
  scanId: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  results: NucleiResult[];
  error?: string;
}

// Helper functions for WebSocket communication
function updateScanProgress(scanId: number, progress: any) {
  if (typeof global !== 'undefined' && global.io && global.scanProgress) {
    const currentProgress = global.scanProgress.get(scanId) || {
      scanId,
      status: 'running',
      progress: 0,
      vulnerabilitiesFound: 0,
      logs: []
    };

    const updatedProgress = { ...currentProgress, ...progress };
    global.scanProgress.set(scanId, updatedProgress);
    global.io.to(`scan-${scanId}`).emit('scan-progress', updatedProgress);
    console.log(`Progress update sent for scan ${scanId}:`, updatedProgress);
  }
}

function addScanLog(scanId: number, log: string) {
  if (typeof global !== 'undefined' && global.scanProgress) {
    const progress = global.scanProgress.get(scanId);
    if (progress) {
      progress.logs = progress.logs || [];
      progress.logs.push(`${new Date().toISOString()}: ${log}`);

      // Keep only last 50 logs
      if (progress.logs.length > 50) {
        progress.logs = progress.logs.slice(-50);
      }

      updateScanProgress(scanId, { logs: progress.logs });
    }
  }
}

class NucleiService {
  private activeScanProcesses: Map<number, ChildProcess> = new Map();
  private scanProgressCallbacks: Map<number, (progress: ScanProgress) => void> = new Map();

  async startScan(
    scanId: number,
    target: string,
    templates: string = 'all',
    onProgress?: (progress: ScanProgress) => void
  ): Promise<void> {
    console.log(`Starting scan ${scanId} for target: ${target} with templates: ${templates}`);

    const startTime = Date.now();

    try {
      // Update scan status to running
      await db.updateScan(scanId, { status: 'running' });

      if (onProgress) {
        this.scanProgressCallbacks.set(scanId, onProgress);
      }

      // Initialize WebSocket progress
      updateScanProgress(scanId, {
        scanId,
        status: 'running',
        progress: 0,
        vulnerabilitiesFound: 0,
        currentTarget: target,
        timeElapsed: 0,
        logs: [`Scan started for target: ${target}`]
      });

      // Prepare output directory
      const outputDir = path.join('/app/reports', scanId.toString());
      await fs.mkdir(outputDir, { recursive: true });

      const outputFile = path.join(outputDir, 'results.json');

      console.log(`Output directory: ${outputDir}`);
      console.log(`Output file: ${outputFile}`);

      addScanLog(scanId, `Output directory created: ${outputDir}`);

      // Build nuclei command with correct flags
      const args = [
        '-u', target,  // Correct flag for target
        '-jsonl',      // JSON Lines output
        '-o', outputFile,
        '-silent',
        '-timeout', '10'
      ];

      // Add template selection with correct flags
      if (templates === 'basic') {
        // Use severity filtering for basic scans
        args.push('-s', 'critical,high,medium');
      }
      // For 'all' templates, don't add any specific flags to use all available templates

      console.log(`Nuclei command: nuclei ${args.join(' ')}`);
      addScanLog(scanId, `Executing: nuclei ${args.join(' ')}`);

      // Spawn nuclei process
      const nucleiProcess = spawn('nuclei', args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env, PATH: process.env.PATH }
      });

      this.activeScanProcesses.set(scanId, nucleiProcess);

      console.log(`Nuclei process started with PID: ${nucleiProcess.pid}`);
      addScanLog(scanId, `Nuclei process started with PID: ${nucleiProcess.pid}`);

      const results: NucleiResult[] = [];
      let progress = 0;
      let vulnerabilitiesFound = 0;
      let templatesProcessed = 0;

      // Handle stdout (results)
      nucleiProcess.stdout?.on('data', (data) => {
        const lines = data.toString().split('\n').filter((line: string) => line.trim());

        for (const line of lines) {
          try {
            const result = JSON.parse(line) as NucleiResult;
            results.push(result);
            vulnerabilitiesFound++;
            progress = Math.min(progress + 2, 95); // Don't go to 100% until complete

            console.log(`Nuclei result: ${JSON.stringify(result)}`);
            addScanLog(scanId, `Vulnerability found: ${result.info?.name || result.template || 'Unknown'}`);

            // Update WebSocket progress
            updateScanProgress(scanId, {
              vulnerabilitiesFound,
              currentTemplate: result.template || result.template_id || 'Unknown',
              progress,
              timeElapsed: Math.floor((Date.now() - startTime) / 1000)
            });

            this.notifyProgress(scanId, {
              scanId,
              status: 'running',
              progress,
              results: [...results]
            });
          } catch {
            // Not JSON, might be status info or template info
            const logLine = line.trim();
            if (logLine) {
              console.log(`Nuclei stdout: ${logLine}`);

              // Try to extract template information
              if (logLine.includes('template') || logLine.includes('Template')) {
                templatesProcessed++;
                updateScanProgress(scanId, {
                  currentTemplate: logLine,
                  progress: Math.min(95, templatesProcessed),
                  timeElapsed: Math.floor((Date.now() - startTime) / 1000)
                });
              }

              addScanLog(scanId, logLine);
            }
          }
        }
      });

      // Handle stderr (errors and stats)
      nucleiProcess.stderr?.on('data', (data) => {
        const message = data.toString();
        console.log(`Nuclei stderr [${scanId}]: ${message}`);

        // Add to WebSocket logs
        addScanLog(scanId, `Nuclei: ${message.trim()}`);

        // Check for common error patterns
        if (message.includes('no templates found') || message.includes('template not found')) {
          console.error(`Template error for scan ${scanId}: ${message}`);
          addScanLog(scanId, `ERROR: ${message.trim()}`);
        }
      });

      // Handle process completion
      nucleiProcess.on('close', async (code, signal) => {
        console.log(`Nuclei process [${scanId}] closed with code: ${code}, signal: ${signal}`);

        this.activeScanProcesses.delete(scanId);
        this.scanProgressCallbacks.delete(scanId);

        if (code === 0) {
          // Scan completed successfully
          console.log(`Scan ${scanId} completed successfully with ${results.length} results`);

          const totalTime = Math.floor((Date.now() - startTime) / 1000);
          addScanLog(scanId, `Scan completed successfully with ${results.length} vulnerabilities found`);
          updateScanProgress(scanId, {
            status: 'completed',
            progress: 100,
            vulnerabilitiesFound: results.length,
            timeElapsed: totalTime,
            results
          });

          await db.updateScan(scanId, {
            status: 'completed',
            results: JSON.stringify(results),
            completed_at: new Date().toISOString()
          });

          this.notifyProgress(scanId, {
            scanId,
            status: 'completed',
            progress: 100,
            results
          });
        } else {
          // Scan failed
          console.error(`Scan ${scanId} failed with exit code: ${code}`);

          const totalTime = Math.floor((Date.now() - startTime) / 1000);
          const errorMsg = `Nuclei process exited with code ${code}`;

          addScanLog(scanId, `ERROR: ${errorMsg}`);
          updateScanProgress(scanId, {
            status: 'failed',
            progress: 0,
            timeElapsed: totalTime,
            error: errorMsg
          });

          await db.updateScan(scanId, {
            status: 'failed',
            completed_at: new Date().toISOString()
          });

          this.notifyProgress(scanId, {
            scanId,
            status: 'failed',
            progress: 0,
            results: [],
            error: errorMsg
          });
        }
      });

      // Handle process errors
      nucleiProcess.on('error', async (error) => {
        console.error(`Nuclei process error:`, error);
        
        this.activeScanProcesses.delete(scanId);
        this.scanProgressCallbacks.delete(scanId);

        await db.updateScan(scanId, {
          status: 'failed',
          completed_at: new Date().toISOString()
        });

        this.notifyProgress(scanId, {
          scanId,
          status: 'failed',
          progress: 0,
          results: [],
          error: error.message
        });
      });

    } catch (error) {
      console.error('Error starting scan:', error);
      
      await db.updateScan(scanId, {
        status: 'failed',
        completed_at: new Date().toISOString()
      });

      this.notifyProgress(scanId, {
        scanId,
        status: 'failed',
        progress: 0,
        results: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private notifyProgress(scanId: number, progress: ScanProgress): void {
    const callback = this.scanProgressCallbacks.get(scanId);
    if (callback) {
      callback(progress);
    }
  }

  async stopScan(scanId: number): Promise<void> {
    const process = this.activeScanProcesses.get(scanId);
    if (process) {
      process.kill('SIGTERM');
      this.activeScanProcesses.delete(scanId);
      this.scanProgressCallbacks.delete(scanId);
      
      await db.updateScan(scanId, {
        status: 'failed',
        completed_at: new Date().toISOString()
      });
    }
  }

  async getScanProgress(scanId: number): Promise<ScanProgress | null> {
    const scan = await db.getScanById(scanId);
    if (!scan) return null;

    const results = scan.results ? JSON.parse(scan.results) : [];
    
    return {
      scanId,
      status: scan.status as any,
      progress: scan.status === 'completed' ? 100 : scan.status === 'running' ? 50 : 0,
      results
    };
  }

  isNucleiAvailable(): boolean {
    try {
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const { execSync } = require('child_process');
      execSync('nuclei -version', { stdio: 'ignore' });
      return true;
    } catch {
      return false;
    }
  }
}

export const nucleiService = new NucleiService();
