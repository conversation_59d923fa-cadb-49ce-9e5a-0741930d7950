import { spawn, ChildProcess } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { db } from './database';

export interface NucleiResult {
  template: string;
  template_id: string;
  template_path: string;
  info: {
    name: string;
    author: string[];
    tags: string[];
    description: string;
    reference: string[];
    severity: string;
    metadata: Record<string, any>;
  };
  type: string;
  host: string;
  matched_at: string;
  extracted_results?: string[];
  request?: string;
  response?: string;
  curl_command?: string;
  matcher_status: boolean;
  timestamp: string;
}

export interface ScanProgress {
  scanId: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  results: NucleiResult[];
  error?: string;
}

class NucleiService {
  private activeScanProcesses: Map<number, ChildProcess> = new Map();
  private scanProgressCallbacks: Map<number, (progress: ScanProgress) => void> = new Map();

  async startScan(
    scanId: number,
    target: string,
    templates: string = 'all',
    onProgress?: (progress: ScanProgress) => void
  ): Promise<void> {
    try {
      // Update scan status to running
      await db.updateScan(scanId, { status: 'running' });

      if (onProgress) {
        this.scanProgressCallbacks.set(scanId, onProgress);
      }

      // Prepare output directory
      const outputDir = path.join(process.cwd(), 'reports', scanId.toString());
      await fs.mkdir(outputDir, { recursive: true });

      const outputFile = path.join(outputDir, 'results.json');

      // Build nuclei command
      const args = [
        '-target', target,
        '-json',
        '-o', outputFile,
        '-stats',
        '-silent'
      ];

      // Add template selection
      if (templates === 'all') {
        args.push('-t', '/root/nuclei-templates/');
      } else if (templates === 'basic') {
        args.push('-t', '/root/nuclei-templates/cves/');
        args.push('-t', '/root/nuclei-templates/vulnerabilities/');
      } else {
        // Custom templates
        args.push('-t', templates);
      }

      // Spawn nuclei process
      const nucleiProcess = spawn('nuclei', args, {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      this.activeScanProcesses.set(scanId, nucleiProcess);

      const results: NucleiResult[] = [];
      let progress = 0;

      // Handle stdout (results)
      nucleiProcess.stdout?.on('data', (data) => {
        const lines = data.toString().split('\n').filter((line: string) => line.trim());
        
        for (const line of lines) {
          try {
            const result = JSON.parse(line) as NucleiResult;
            results.push(result);
            progress = Math.min(progress + 1, 95); // Don't go to 100% until complete
            
            this.notifyProgress(scanId, {
              scanId,
              status: 'running',
              progress,
              results: [...results]
            });
          } catch {
            // Ignore non-JSON lines (stats, etc.)
          }
        }
      });

      // Handle stderr (errors and stats)
      nucleiProcess.stderr?.on('data', (data) => {
        console.log(`Nuclei stderr: ${data}`);
      });

      // Handle process completion
      nucleiProcess.on('close', async (code) => {
        this.activeScanProcesses.delete(scanId);
        this.scanProgressCallbacks.delete(scanId);

        if (code === 0) {
          // Scan completed successfully
          await db.updateScan(scanId, {
            status: 'completed',
            results: JSON.stringify(results),
            completed_at: new Date().toISOString()
          });

          this.notifyProgress(scanId, {
            scanId,
            status: 'completed',
            progress: 100,
            results
          });
        } else {
          // Scan failed
          await db.updateScan(scanId, {
            status: 'failed',
            completed_at: new Date().toISOString()
          });

          this.notifyProgress(scanId, {
            scanId,
            status: 'failed',
            progress: 0,
            results: [],
            error: `Nuclei process exited with code ${code}`
          });
        }
      });

      // Handle process errors
      nucleiProcess.on('error', async (error) => {
        console.error(`Nuclei process error:`, error);
        
        this.activeScanProcesses.delete(scanId);
        this.scanProgressCallbacks.delete(scanId);

        await db.updateScan(scanId, {
          status: 'failed',
          completed_at: new Date().toISOString()
        });

        this.notifyProgress(scanId, {
          scanId,
          status: 'failed',
          progress: 0,
          results: [],
          error: error.message
        });
      });

    } catch (error) {
      console.error('Error starting scan:', error);
      
      await db.updateScan(scanId, {
        status: 'failed',
        completed_at: new Date().toISOString()
      });

      this.notifyProgress(scanId, {
        scanId,
        status: 'failed',
        progress: 0,
        results: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private notifyProgress(scanId: number, progress: ScanProgress): void {
    const callback = this.scanProgressCallbacks.get(scanId);
    if (callback) {
      callback(progress);
    }
  }

  async stopScan(scanId: number): Promise<void> {
    const process = this.activeScanProcesses.get(scanId);
    if (process) {
      process.kill('SIGTERM');
      this.activeScanProcesses.delete(scanId);
      this.scanProgressCallbacks.delete(scanId);
      
      await db.updateScan(scanId, {
        status: 'failed',
        completed_at: new Date().toISOString()
      });
    }
  }

  async getScanProgress(scanId: number): Promise<ScanProgress | null> {
    const scan = await db.getScanById(scanId);
    if (!scan) return null;

    const results = scan.results ? JSON.parse(scan.results) : [];
    
    return {
      scanId,
      status: scan.status as any,
      progress: scan.status === 'completed' ? 100 : scan.status === 'running' ? 50 : 0,
      results
    };
  }

  isNucleiAvailable(): boolean {
    try {
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const { execSync } = require('child_process');
      execSync('nuclei -version', { stdio: 'ignore' });
      return true;
    } catch {
      return false;
    }
  }
}

export const nucleiService = new NucleiService();
