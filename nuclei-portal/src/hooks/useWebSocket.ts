import { useEffect, useState, useRef } from 'react';
import { io, Socket } from 'socket.io-client';

export interface ScanProgress {
  scanId: number;
  status: 'running' | 'completed' | 'failed';
  progress: number;
  currentTemplate?: string;
  vulnerabilitiesFound: number;
  totalTemplates?: number;
  currentTarget?: string;
  timeElapsed?: number;
  estimatedTimeRemaining?: number;
  results?: any[];
  error?: string;
  logs?: string[];
}

export function useWebSocket(scanId: number | null) {
  const [progress, setProgress] = useState<ScanProgress | null>(null);
  const [connected, setConnected] = useState(false);
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    if (!scanId) return;

    // Initialize socket connection
    const socket = io({
      path: '/api/socket',
      transports: ['websocket', 'polling']
    });

    socketRef.current = socket;

    socket.on('connect', () => {
      console.log('WebSocket connected');
      setConnected(true);
      
      // Join the scan room
      socket.emit('join-scan', scanId);
    });

    socket.on('disconnect', () => {
      console.log('WebSocket disconnected');
      setConnected(false);
    });

    socket.on('scan-progress', (data: ScanProgress) => {
      console.log('Received scan progress:', data);
      setProgress(data);
    });

    socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      setConnected(false);
    });

    return () => {
      if (socket) {
        socket.emit('leave-scan', scanId);
        socket.disconnect();
      }
    };
  }, [scanId]);

  const joinScan = (newScanId: number) => {
    if (socketRef.current && connected) {
      socketRef.current.emit('join-scan', newScanId);
    }
  };

  const leaveScan = (oldScanId: number) => {
    if (socketRef.current && connected) {
      socketRef.current.emit('leave-scan', oldScanId);
    }
  };

  return {
    progress,
    connected,
    joinScan,
    leaveScan
  };
}
