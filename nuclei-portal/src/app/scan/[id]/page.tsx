'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import ScanProgress from '@/components/ScanProgress';
import { ArrowLeft, Download, Eye } from 'lucide-react';

interface ScanDetails {
  id: number;
  target: string;
  status: string;
  templates: string;
  created_at: string;
  completed_at?: string;
  results?: string;
}

export default function ScanPage() {
  const params = useParams();
  const router = useRouter();
  const scanId = parseInt(params.id as string);
  const [scanDetails, setScanDetails] = useState<ScanDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchScanDetails();
  }, [scanId]);

  const fetchScanDetails = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        router.push('/');
        return;
      }

      const response = await fetch(`/api/scans/${scanId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch scan details');
      }

      const data = await response.json();
      setScanDetails(data.scan);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const handleScanComplete = () => {
    // Refresh scan details when scan completes
    fetchScanDetails();
  };

  const handleScanError = (errorMsg: string) => {
    setError(errorMsg);
    fetchScanDetails();
  };

  const downloadResults = () => {
    if (!scanDetails?.results) return;

    const results = JSON.parse(scanDetails.results);
    const blob = new Blob([JSON.stringify(results, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `nuclei-scan-${scanId}-results.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const viewResults = () => {
    router.push(`/scan/${scanId}/results`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow p-6 max-w-md w-full">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Error</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => router.push('/dashboard')}
              className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!scanDetails) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Scan Not Found</h2>
          <p className="text-gray-600 mb-4">The requested scan could not be found.</p>
          <button
            onClick={() => router.push('/dashboard')}
            className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/dashboard')}
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
                <span>Back to Dashboard</span>
              </button>
            </div>

            <div className="flex items-center space-x-3">
              {scanDetails.status === 'completed' && scanDetails.results && (
                <>
                  <button
                    onClick={viewResults}
                    className="flex items-center space-x-2 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors"
                  >
                    <Eye className="h-4 w-4" />
                    <span>View Results</span>
                  </button>
                  <button
                    onClick={downloadResults}
                    className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
                  >
                    <Download className="h-4 w-4" />
                    <span>Download</span>
                  </button>
                </>
              )}
            </div>
          </div>

          <div className="mt-4">
            <h1 className="text-3xl font-bold text-gray-900">
              Nuclei Scan #{scanId}
            </h1>
            <div className="mt-2 flex items-center space-x-4 text-sm text-gray-600">
              <span>Target: <span className="font-medium">{scanDetails.target}</span></span>
              <span>Templates: <span className="font-medium">{scanDetails.templates}</span></span>
              <span>Started: <span className="font-medium">{new Date(scanDetails.created_at).toLocaleString()}</span></span>
            </div>
          </div>
        </div>

        {/* Progress Component */}
        <ScanProgress
          scanId={scanId}
          onComplete={handleScanComplete}
          onError={handleScanError}
        />
      </div>
    </div>
  );
}
