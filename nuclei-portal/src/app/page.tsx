'use client';

import { useState, useEffect } from 'react';
import { Shield, Target, FileText, BarChart3, Settings, ArrowLeft, Play, Clock, CheckCircle, XCircle } from 'lucide-react';

export default function Home() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [currentView, setCurrentView] = useState('dashboard');

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      // Verify token and get user info
      setIsAuthenticated(true);
    }
  }, []);

  if (!isAuthenticated) {
    return <LoginPage onLogin={setIsAuthenticated} setUser={setUser} />;
  }

  return <Dashboard user={user} currentView={currentView} setCurrentView={setCurrentView} />;
}

function LoginPage({ onLogin, setUser }: { onLogin: (auth: boolean) => void; setUser: (user: any) => void }) {
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const endpoint = isLogin ? '/api/auth/login' : '/api/auth/register';
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (response.ok) {
        localStorage.setItem('token', data.token);
        setUser(data.user);
        onLogin(true);
      } else {
        setError(data.error || 'Authentication failed');
      }
    } catch {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-xl p-8">
        <div className="text-center mb-8">
          <Shield className="mx-auto h-12 w-12 text-indigo-600 mb-4" />
          <h1 className="text-3xl font-bold text-gray-900">Nuclei Portal</h1>
          <p className="text-gray-600 mt-2">Professional Vulnerability Scanner</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              required
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm text-center">{error}</div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {loading ? 'Processing...' : (isLogin ? 'Sign In' : 'Sign Up')}
          </button>
        </form>

        <div className="mt-6 text-center">
          <button
            onClick={() => setIsLogin(!isLogin)}
            className="text-indigo-600 hover:text-indigo-500 text-sm"
          >
            {isLogin ? "Don't have an account? Sign up" : "Already have an account? Sign in"}
          </button>
        </div>

        <div className="mt-6 p-4 bg-gray-50 rounded-md">
          <p className="text-sm text-gray-600 text-center">
            Demo credentials:<br />
            <strong><EMAIL></strong><br />
            <strong>admin123</strong>
          </p>
        </div>
      </div>
    </div>
  );
}

function Dashboard({ user, currentView, setCurrentView }: {
  user: any;
  currentView: string;
  setCurrentView: (view: string) => void;
}) {
  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-indigo-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">Nuclei Portal</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome back!</span>
              <button
                onClick={() => {
                  localStorage.removeItem('token');
                  window.location.reload();
                }}
                className="text-sm text-indigo-600 hover:text-indigo-500"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {currentView === 'dashboard' && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <DashboardCard
                  title="New Scan"
                  description="Start a vulnerability scan"
                  icon={Target}
                  color="bg-blue-500"
                  onClick={() => setCurrentView('new-scan')}
                />
                <DashboardCard
                  title="Reports"
                  description="View scan reports"
                  icon={FileText}
                  color="bg-green-500"
                  onClick={() => setCurrentView('reports')}
                />
                <DashboardCard
                  title="Analytics"
                  description="Scan statistics"
                  icon={BarChart3}
                  color="bg-purple-500"
                  onClick={() => setCurrentView('analytics')}
                />
                <DashboardCard
                  title="Settings"
                  description="Configure portal"
                  icon={Settings}
                  color="bg-gray-500"
                  onClick={() => setCurrentView('settings')}
                />
              </div>

              <RecentScans user={user} />
            </>
          )}

          {currentView === 'new-scan' && (
            <NewScanForm user={user} onBack={() => setCurrentView('dashboard')} />
          )}

          {currentView === 'reports' && (
            <ReportsView user={user} onBack={() => setCurrentView('dashboard')} />
          )}

          {currentView === 'analytics' && (
            <AnalyticsView user={user} onBack={() => setCurrentView('dashboard')} />
          )}

          {currentView === 'settings' && (
            <SettingsView user={user} onBack={() => setCurrentView('dashboard')} />
          )}
        </div>
      </main>
    </div>
  );
}

function DashboardCard({ title, description, icon: Icon, color, onClick }: {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  onClick?: () => void;
}) {
  return (
    <div
      className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow cursor-pointer"
      onClick={onClick}
    >
      <div className="flex items-center">
        <div className={`${color} p-3 rounded-lg`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4">
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
      </div>
    </div>
  );
}

function RecentScans({ user }: { user: any }) {
  const [scans, setScans] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchScans();
  }, []);

  const fetchScans = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/scans', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setScans(data.scans || []);
      }
    } catch (error) {
      console.error('Error fetching scans:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'running':
        return <Clock className="h-5 w-5 text-blue-500" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-lg font-medium text-gray-900 mb-4">Recent Scans</h2>

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading scans...</p>
        </div>
      ) : scans.length === 0 ? (
        <div className="text-center py-12 text-gray-500">
          <Target className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p>No scans yet. Start your first vulnerability scan!</p>
        </div>
      ) : (
        <div className="space-y-4">
          {scans.map((scan) => (
            <div key={scan.id} className="border rounded-lg p-4 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(scan.status)}
                  <div>
                    <h3 className="font-medium text-gray-900">{scan.target}</h3>
                    <p className="text-sm text-gray-500">
                      {new Date(scan.created_at).toLocaleDateString()} - {scan.templates}
                    </p>
                  </div>
                </div>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  scan.status === 'completed' ? 'bg-green-100 text-green-800' :
                  scan.status === 'running' ? 'bg-blue-100 text-blue-800' :
                  scan.status === 'failed' ? 'bg-red-100 text-red-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {scan.status}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

function NewScanForm({ user, onBack }: { user: any; onBack: () => void }) {
  const [target, setTarget] = useState('');
  const [templates, setTemplates] = useState('basic');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/scans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ target, templates })
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(`Scan started successfully! Scan ID: ${data.scan.id}`);
        setTarget('');
        setTimeout(() => {
          onBack();
        }, 2000);
      } else {
        setError(data.error || 'Failed to start scan');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center mb-6">
        <button
          onClick={onBack}
          className="mr-4 p-2 hover:bg-gray-100 rounded-lg"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>
        <h2 className="text-lg font-medium text-gray-900">New Vulnerability Scan</h2>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Target URL or IP
          </label>
          <input
            type="text"
            value={target}
            onChange={(e) => setTarget(e.target.value)}
            placeholder="https://example.com or ***********"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
          />
          <p className="mt-1 text-sm text-gray-500">
            Enter a URL, IP address, or domain name to scan
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Template Selection
          </label>
          <select
            value={templates}
            onChange={(e) => setTemplates(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="basic">Basic Templates (CVEs + Vulnerabilities)</option>
            <option value="all">All Templates (Comprehensive Scan)</option>
          </select>
          <p className="mt-1 text-sm text-gray-500">
            {templates === 'basic'
              ? 'Fast scan with essential vulnerability checks'
              : 'Complete scan with all available templates (slower)'
            }
          </p>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <Shield className="h-5 w-5 text-blue-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Your Plan: {user?.plan || 'Free'}
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>Scans used: {user?.scans_used || 0} / {user?.scans_limit || 10}</p>
              </div>
            </div>
          </div>
        </div>

        {error && (
          <div className="text-red-600 text-sm bg-red-50 border border-red-200 rounded-md p-3">
            {error}
          </div>
        )}

        {success && (
          <div className="text-green-600 text-sm bg-green-50 border border-green-200 rounded-md p-3">
            {success}
          </div>
        )}

        <div className="flex space-x-4">
          <button
            type="submit"
            disabled={loading}
            className="flex-1 bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50 flex items-center justify-center"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Starting Scan...
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Start Scan
              </>
            )}
          </button>

          <button
            type="button"
            onClick={onBack}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}

function ReportsView({ user, onBack }: { user: any; onBack: () => void }) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center mb-6">
        <button onClick={onBack} className="mr-4 p-2 hover:bg-gray-100 rounded-lg">
          <ArrowLeft className="h-5 w-5" />
        </button>
        <h2 className="text-lg font-medium text-gray-900">Scan Reports</h2>
      </div>

      <div className="text-center py-12 text-gray-500">
        <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <p>Reports feature coming soon!</p>
        <p className="text-sm mt-2">Generate and download professional HTML reports</p>
      </div>
    </div>
  );
}

function AnalyticsView({ user, onBack }: { user: any; onBack: () => void }) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center mb-6">
        <button onClick={onBack} className="mr-4 p-2 hover:bg-gray-100 rounded-lg">
          <ArrowLeft className="h-5 w-5" />
        </button>
        <h2 className="text-lg font-medium text-gray-900">Analytics</h2>
      </div>

      <div className="text-center py-12 text-gray-500">
        <BarChart3 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <p>Analytics dashboard coming soon!</p>
        <p className="text-sm mt-2">View scan statistics and vulnerability trends</p>
      </div>
    </div>
  );
}

function SettingsView({ user, onBack }: { user: any; onBack: () => void }) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center mb-6">
        <button onClick={onBack} className="mr-4 p-2 hover:bg-gray-100 rounded-lg">
          <ArrowLeft className="h-5 w-5" />
        </button>
        <h2 className="text-lg font-medium text-gray-900">Settings</h2>
      </div>

      <div className="space-y-6">
        <div>
          <h3 className="text-md font-medium text-gray-900 mb-4">Account Information</h3>
          <div className="bg-gray-50 rounded-lg p-4">
            <p><strong>Email:</strong> {user?.email}</p>
            <p><strong>Plan:</strong> {user?.plan}</p>
            <p><strong>Scans Used:</strong> {user?.scans_used} / {user?.scans_limit}</p>
          </div>
        </div>

        <div className="text-center py-8 text-gray-500">
          <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p>Advanced settings coming soon!</p>
          <p className="text-sm mt-2">Configure API keys, notifications, and more</p>
        </div>
      </div>
    </div>
  );
}
