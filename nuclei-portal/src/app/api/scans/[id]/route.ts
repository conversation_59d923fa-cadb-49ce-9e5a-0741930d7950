import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';
import { db } from '@/lib/database';
import { nucleiService } from '@/lib/nuclei';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authHeader = request.headers.get('authorization');
    const token = AuthService.extractTokenFromHeader(authHeader);
    
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await AuthService.verifyToken(token);
    if (!user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const resolvedParams = await params;
    const scanId = parseInt(resolvedParams.id);
    const scan = await db.getScanById(scanId);

    if (!scan) {
      return NextResponse.json({ error: 'Scan not found' }, { status: 404 });
    }

    // Check if user owns this scan
    if (scan.user_id !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get scan progress
    const progress = await nucleiService.getScanProgress(scanId);

    return NextResponse.json({
      scan: {
        id: scan.id,
        target: scan.target,
        status: scan.status,
        templates: scan.templates,
        created_at: scan.created_at,
        completed_at: scan.completed_at,
        results: scan.results ? JSON.parse(scan.results) : []
      },
      progress
    });

  } catch (error) {
    console.error('Get scan error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authHeader = request.headers.get('authorization');
    const token = AuthService.extractTokenFromHeader(authHeader);
    
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await AuthService.verifyToken(token);
    if (!user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const resolvedParams = await params;
    const scanId = parseInt(resolvedParams.id);
    const scan = await db.getScanById(scanId);

    if (!scan) {
      return NextResponse.json({ error: 'Scan not found' }, { status: 404 });
    }

    // Check if user owns this scan
    if (scan.user_id !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Stop the scan if it's running
    if (scan.status === 'running') {
      await nucleiService.stopScan(scanId);
    }

    return NextResponse.json({ message: 'Scan stopped successfully' });

  } catch (error) {
    console.error('Stop scan error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
