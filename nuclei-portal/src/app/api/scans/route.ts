import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';
import { db } from '@/lib/database';
import { nucleiService } from '@/lib/nuclei';

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    const token = AuthService.extractTokenFromHeader(authHeader);
    
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await AuthService.verifyToken(token);
    if (!user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const scans = await db.getUserScans(user.id);
    
    return NextResponse.json({ scans });

  } catch (error) {
    console.error('Get scans error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    const token = AuthService.extractTokenFromHeader(authHeader);
    
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await AuthService.verifyToken(token);
    if (!user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check scan limits
    if (user.scans_used >= user.scans_limit) {
      return NextResponse.json(
        { error: 'Scan limit reached. Please upgrade your plan.' },
        { status: 403 }
      );
    }

    const { target, templates = 'basic' } = await request.json();

    if (!target) {
      return NextResponse.json(
        { error: 'Target is required' },
        { status: 400 }
      );
    }

    // Validate target format (basic validation)
    const urlPattern = /^https?:\/\/.+/;
    const ipPattern = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;
    const domainPattern = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;

    if (!urlPattern.test(target) && !ipPattern.test(target) && !domainPattern.test(target)) {
      return NextResponse.json(
        { error: 'Invalid target format. Use URL, IP address, or domain name.' },
        { status: 400 }
      );
    }

    // Create scan record
    const scan = await db.createScan(user.id, target, templates);
    
    // Increment user scan count
    await db.incrementUserScans(user.id);

    // Start the scan asynchronously
    nucleiService.startScan(scan.id, target, templates);

    return NextResponse.json({
      scan: {
        id: scan.id,
        target: scan.target,
        status: scan.status,
        templates: scan.templates,
        created_at: scan.created_at
      }
    });

  } catch (error) {
    console.error('Create scan error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
