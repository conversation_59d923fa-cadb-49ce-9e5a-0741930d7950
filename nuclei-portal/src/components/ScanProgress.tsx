import React, { useEffect, useState } from 'react';
import { useWebSocket, ScanProgress as ScanProgressType } from '@/hooks/useWebSocket';
import { 
  Activity, 
  Clock, 
  Target, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Terminal,
  Zap,
  Shield
} from 'lucide-react';

interface ScanProgressProps {
  scanId: number;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

export default function ScanProgress({ scanId, onComplete, onError }: ScanProgressProps) {
  const { progress, connected } = useWebSocket(scanId);
  const [showLogs, setShowLogs] = useState(false);

  useEffect(() => {
    if (progress?.status === 'completed' && onComplete) {
      onComplete();
    } else if (progress?.status === 'failed' && onError) {
      onError(progress.error || 'Scan failed');
    }
  }, [progress?.status, onComplete, onError]);

  if (!progress) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-center space-x-3">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
          <span className="text-gray-600">Initializing scan...</span>
        </div>
      </div>
    );
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusIcon = () => {
    switch (progress.status) {
      case 'running':
        return <Activity className="h-5 w-5 text-blue-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (progress.status) {
      case 'running':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-green-500';
      case 'failed':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Scan Progress - ID #{scanId}
            </h3>
            <p className="text-sm text-gray-500">
              {connected ? 'Connected' : 'Disconnected'} • {progress.status}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          {progress.timeElapsed !== undefined && (
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Clock className="h-4 w-4" />
              <span>{formatTime(progress.timeElapsed)}</span>
            </div>
          )}
          
          <button
            onClick={() => setShowLogs(!showLogs)}
            className="flex items-center space-x-2 px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            <Terminal className="h-4 w-4" />
            <span>{showLogs ? 'Hide' : 'Show'} Logs</span>
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Progress</span>
          <span className="font-medium">{progress.progress}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div
            className={`h-3 rounded-full transition-all duration-300 ${getStatusColor()}`}
            style={{ width: `${progress.progress}%` }}
          ></div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <Shield className="h-8 w-8 text-red-500" />
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {progress.vulnerabilitiesFound}
              </p>
              <p className="text-sm text-gray-600">Vulnerabilities Found</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <Target className="h-8 w-8 text-blue-500" />
            <div>
              <p className="text-sm font-medium text-gray-900 truncate">
                {progress.currentTarget || 'N/A'}
              </p>
              <p className="text-sm text-gray-600">Current Target</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <Zap className="h-8 w-8 text-yellow-500" />
            <div>
              <p className="text-sm font-medium text-gray-900 truncate">
                {progress.currentTemplate || 'Initializing...'}
              </p>
              <p className="text-sm text-gray-600">Current Template</p>
            </div>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {progress.status === 'failed' && progress.error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-red-800">Scan Failed</h4>
              <p className="text-sm text-red-700 mt-1">{progress.error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {progress.status === 'completed' && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex items-start space-x-3">
            <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-green-800">Scan Completed</h4>
              <p className="text-sm text-green-700 mt-1">
                Found {progress.vulnerabilitiesFound} vulnerabilities in {formatTime(progress.timeElapsed || 0)}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Live Logs */}
      {showLogs && progress.logs && (
        <div className="bg-gray-900 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <Terminal className="h-4 w-4 text-green-400" />
            <span className="text-sm font-medium text-green-400">Live Logs</span>
          </div>
          <div className="max-h-64 overflow-y-auto space-y-1">
            {progress.logs.slice(-20).map((log, index) => (
              <div key={index} className="text-xs font-mono text-gray-300">
                {log}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
