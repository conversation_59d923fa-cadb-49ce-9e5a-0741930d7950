const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Nuclei Portal...');

// Check if Nuclei is available
const nucleiCheck = spawn('nuclei', ['-version'], { stdio: 'pipe' });

nucleiCheck.on('close', (code) => {
  if (code === 0) {
    console.log('✅ Nuclei is available');
    startServer();
  } else {
    console.error('❌ Nuclei is not available. Please install Nuclei first.');
    process.exit(1);
  }
});

nucleiCheck.on('error', (error) => {
  console.error('❌ Error checking Nuclei:', error.message);
  console.error('Please install Nuclei first: https://github.com/projectdiscovery/nuclei');
  process.exit(1);
});

function startServer() {
  console.log('🔧 Starting Next.js server...');
  
  // Set environment variables
  process.env.NODE_ENV = process.env.NODE_ENV || 'development';
  
  // Start the server
  const server = spawn('node', ['server.js'], {
    stdio: 'inherit',
    cwd: __dirname
  });

  server.on('error', (error) => {
    console.error('❌ Error starting server:', error.message);
    process.exit(1);
  });

  server.on('close', (code) => {
    console.log(`Server process exited with code ${code}`);
    process.exit(code);
  });

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    server.kill('SIGINT');
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down server...');
    server.kill('SIGTERM');
  });
} 